import { useState } from 'react';
import { 
  ListTodo, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Filter,
  Search,
  Plus,
  Eye,
  Trash2,
  <PERSON>otateCcw,
  User,
  Calendar,
  Tag
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import type { TaskStatus, TaskPriority } from '../store/appStore';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';
import { toast } from 'sonner';

type TaskType = 'document_analysis' | 'content_processing' | 'summary_generation' | 'entity_extraction' | 'sentiment_analysis';

interface TaskFilter {
  status: import('../store/appStore').TaskStatus | 'all';
  type: TaskType | 'all';
  priority: import('../store/appStore').TaskPriority | 'all';
  assignedAgent: string | 'all';
  searchQuery: string;
}

interface NewTaskForm {
  type: TaskType;
  priority: import('../store/appStore').TaskPriority;
  documentId?: string;
  description: string;
  assignedAgent?: string;
}

export default function TaskManager() {
  const { tasks, agents, documents, addTask, updateTask, removeTask } = useAppStore();
  const { } = useSocket();
  const [filter, setFilter] = useState<TaskFilter>({
    status: 'all',
    type: 'all',
    priority: 'all',
    assignedAgent: 'all',
    searchQuery: ''
  });
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTask, setNewTask] = useState<NewTaskForm>({
    type: 'document_analysis',
    priority: 'normal',
    description: ''
  });
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    if (filter.status !== 'all' && task.status !== filter.status) return false;
    if (filter.type !== 'all' && task.type !== filter.type) return false;
    if (filter.priority !== 'all' && task.priority !== filter.priority) return false;
    if (filter.assignedAgent !== 'all' && task.assignedAgent !== filter.assignedAgent) return false;
    if (filter.searchQuery && !task.type.toLowerCase().includes(filter.searchQuery.toLowerCase()) &&
        !task.description?.toLowerCase().includes(filter.searchQuery.toLowerCase())) return false;
    return true;
  });

  // 创建新任务
  const createTask = async () => {
    try {
      const taskData = {
        id: Math.random().toString(36).substr(2, 9),
        type: newTask.type,
        status: 'pending' as import('../store/appStore').TaskStatus,
        priority: newTask.priority,
        description: newTask.description,
        documentId: newTask.documentId,
        assignedAgent: newTask.assignedAgent,
        createdAt: new Date().toISOString(),
        progress: 0
      };

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });

      if (!response.ok) {
        throw new Error('创建任务失败');
      }

      const result = await response.json();
      addTask(result);
      
      setShowCreateForm(false);
      setNewTask({
        type: 'document_analysis',
        priority: 'normal',
        description: ''
      });
      
      toast.success('任务创建成功');
    } catch (error) {
      console.error('Create task error:', error);
      toast.error('任务创建失败');
    }
  };

  // 控制任务
  const controlTask = async (taskId: string, action: 'start' | 'pause' | 'cancel' | 'retry') => {
    try {
      const response = await fetch(`/api/tasks/${taskId}/${action}`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('操作失败');
      }

      const newStatus: import('../store/appStore').TaskStatus = 
        action === 'start' ? 'running' :
        action === 'pause' ? 'pending' :
        action === 'cancel' ? 'error' :
        'pending';

      updateTask(taskId, { status: newStatus });
      toast.success(`任务${action}操作成功`);
    } catch (error) {
      console.error('Control task error:', error);
      toast.error('任务操作失败');
    }
  };

  // 删除任务
  const deleteTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('删除失败');
      }

      removeTask(taskId);
      toast.success('任务删除成功');
    } catch (error) {
      console.error('Delete task error:', error);
      toast.error('任务删除失败');
    }
  };

  // 批量操作
  const batchOperation = async (action: 'start' | 'pause' | 'cancel' | 'delete') => {
    try {
      const promises = selectedTasks.map(taskId => {
        if (action === 'delete') {
          return deleteTask(taskId);
        } else {
          return controlTask(taskId, action);
        }
      });

      await Promise.all(promises);
      setSelectedTasks([]);
      toast.success(`批量${action}操作完成`);
    } catch (error) {
      console.error('Batch operation error:', error);
      toast.error('批量操作失败');
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: import('../store/appStore').TaskStatus) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'running':
        return 'text-blue-600 bg-blue-100';
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: import('../store/appStore').TaskPriority) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'normal':
        return 'text-blue-600 bg-blue-100';
      case 'low':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: import('../store/appStore').TaskStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'running':
        return <Play className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <ListTodo className="w-4 h-4" />;
    }
  };

  // 格式化时间
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return '刚刚';
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
    return `${Math.floor(diffInMinutes / 1440)}天前`;
  };

  // 获取智能体名称
  const getAgentName = (agentId?: string) => {
    if (!agentId) return '未分配';
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : agentId;
  };

  // 获取文档名称
  const getDocumentName = (docId?: string) => {
    if (!docId) return '无关联文档';
    const doc = documents.find(d => d.id === docId);
    return doc ? doc.name : docId;
  };

  // 统计数据
  const stats = {
    total: tasks.length,
    pending: tasks.filter(t => t.status === 'pending').length,
    running: tasks.filter(t => t.status === 'running').length,
    completed: tasks.filter(t => t.status === 'completed').length,
    failed: tasks.filter(t => t.status === 'error').length
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">任务管理</h1>
          <p className="text-muted-foreground mt-1">
            管理和监控系统中的所有任务
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {selectedTasks.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedTasks.length} 个任务
              </span>
              <button
                onClick={() => batchOperation('start')}
                className="btn-secondary btn-sm"
              >
                <Play className="w-4 h-4 mr-1" />
                批量启动
              </button>
              <button
                onClick={() => batchOperation('pause')}
                className="btn-secondary btn-sm"
              >
                <Pause className="w-4 h-4 mr-1" />
                批量暂停
              </button>
              <button
                onClick={() => batchOperation('delete')}
                className="btn-secondary btn-sm text-red-600"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                批量删除
              </button>
            </div>
          )}
          
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn-primary"
          >
            <Plus className="w-4 h-4 mr-2" />
            创建任务
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-card rounded-lg border p-4">
          <div className="text-2xl font-bold text-foreground">{stats.total}</div>
          <div className="text-sm text-muted-foreground">总任务数</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          <div className="text-sm text-muted-foreground">等待中</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
          <div className="text-sm text-muted-foreground">进行中</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-muted-foreground">已完成</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-muted-foreground">失败</div>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="bg-card rounded-lg border p-4">
        <div className="flex items-center space-x-4 flex-wrap gap-2">
          <div className="flex items-center space-x-2">
            <Search className="w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="搜索任务..."
              value={filter.searchQuery}
              onChange={(e) => setFilter(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="input-field w-64"
            />
          </div>
          
          <select
            value={filter.status}
            onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value as TaskStatus | 'all' }))}
            className="input-field"
          >
            <option value="all">所有状态</option>
            <option value="pending">等待中</option>
            <option value="running">进行中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
            <option value="cancelled">已取消</option>
          </select>
          
          <select
            value={filter.type}
            onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value as TaskType | 'all' }))}
            className="input-field"
          >
            <option value="all">所有类型</option>
            <option value="document_analysis">文档分析</option>
            <option value="content_processing">内容处理</option>
            <option value="summary_generation">摘要生成</option>
            <option value="entity_extraction">实体提取</option>
            <option value="sentiment_analysis">情感分析</option>
          </select>
          
          <select
            value={filter.priority}
            onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value as TaskPriority | 'all' }))}
            className="input-field"
          >
            <option value="all">所有优先级</option>
            <option value="urgent">紧急</option>
            <option value="high">高</option>
            <option value="medium">中</option>
            <option value="low">低</option>
          </select>
          
          <select
            value={filter.assignedAgent}
            onChange={(e) => setFilter(prev => ({ ...prev, assignedAgent: e.target.value }))}
            className="input-field"
          >
            <option value="all">所有智能体</option>
            {agents.map(agent => (
              <option key={agent.id} value={agent.id}>{agent.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="bg-card rounded-lg border">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-foreground">任务列表</h3>
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                显示 {filteredTasks.length} / {tasks.length} 个任务
              </span>
            </div>
          </div>
        </div>
        
        <div className="divide-y">
          {filteredTasks.map((task) => (
            <div key={task.id} className="p-4 hover:bg-muted/50 transition-colors">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedTasks.includes(task.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTasks(prev => [...prev, task.id]);
                    } else {
                      setSelectedTasks(prev => prev.filter(id => id !== task.id));
                    }
                  }}
                  className="rounded border-gray-300"
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={cn(
                      "status-indicator text-xs px-2 py-1 rounded-full flex items-center space-x-1",
                      getStatusColor(task.status)
                    )}>
                      {getStatusIcon(task.status)}
                      <span>{task.status}</span>
                    </span>
                    
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full",
                      getPriorityColor(task.priority)
                    )}>
                      {task.priority}
                    </span>
                    
                    <Tag className="w-3 h-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {task.type.replace('_', ' ')}
                    </span>
                  </div>
                  
                  <h4 className="font-medium text-foreground mb-1">
                    {task.description || task.type.replace('_', ' ')}
                  </h4>
                  
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <User className="w-3 h-3" />
                      <span>{getAgentName(task.assignedAgent)}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatTimeAgo(task.createdAt)}</span>
                    </div>
                    
                    {task.documentId && (
                      <div className="flex items-center space-x-1">
                        <ListTodo className="w-3 h-3" />
                        <span>{getDocumentName(task.documentId)}</span>
                      </div>
                    )}
                  </div>
                  
                  {task.progress !== undefined && task.status === 'running' && (
                    <div className="mt-2">
                      <div className="progress-bar">
                        <div 
                          className="progress-fill" 
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {task.progress}% 完成
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-1">
                  {task.status === 'pending' && (
                    <button
                      onClick={() => controlTask(task.id, 'start')}
                      className="p-2 text-green-500 hover:bg-green-100 rounded transition-colors"
                      title="启动任务"
                    >
                      <Play className="w-4 h-4" />
                    </button>
                  )}
                  
                  {task.status === 'running' && (
                    <button
                      onClick={() => controlTask(task.id, 'pause')}
                      className="p-2 text-yellow-500 hover:bg-yellow-100 rounded transition-colors"
                      title="暂停任务"
                    >
                      <Pause className="w-4 h-4" />
                    </button>
                  )}
                  
                  {task.status === 'error' && (
                    <button
                      onClick={() => controlTask(task.id, 'retry')}
                      className="p-2 text-blue-500 hover:bg-blue-100 rounded transition-colors"
                      title="重试任务"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                  )}
                  
                  <button
                    className="p-2 text-blue-500 hover:bg-blue-100 rounded transition-colors"
                    title="查看详情"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => deleteTask(task.id)}
                    className="p-2 text-red-500 hover:bg-red-100 rounded transition-colors"
                    title="删除任务"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredTasks.length === 0 && (
          <div className="p-8 text-center">
            <ListTodo className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">没有找到任务</h3>
            <p className="text-muted-foreground">
              {filter.searchQuery || filter.status !== 'all' || filter.type !== 'all' 
                ? '尝试调整过滤条件' 
                : '点击上方按钮创建第一个任务'}
            </p>
          </div>
        )}
      </div>

      {/* 创建任务对话框 */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg border p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-foreground mb-4">创建新任务</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  任务类型
                </label>
                <select
                  value={newTask.type}
                  onChange={(e) => setNewTask(prev => ({ ...prev, type: e.target.value as TaskType }))}
                  className="input-field w-full"
                >
                  <option value="document_analysis">文档分析</option>
                  <option value="content_processing">内容处理</option>
                  <option value="summary_generation">摘要生成</option>
                  <option value="entity_extraction">实体提取</option>
                  <option value="sentiment_analysis">情感分析</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  优先级
                </label>
                <select
                  value={newTask.priority}
                  onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value as import('../store/appStore').TaskPriority }))}
                  className="input-field w-full"
                >
                  <option value="low">低</option>
                  <option value="normal">中</option>
                  <option value="high">高</option>
                  <option value="urgent">紧急</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  关联文档
                </label>
                <select
                  value={newTask.documentId || ''}
                  onChange={(e) => setNewTask(prev => ({ ...prev, documentId: e.target.value || undefined }))}
                  className="input-field w-full"
                >
                  <option value="">无关联文档</option>
                  {documents.map(doc => (
                    <option key={doc.id} value={doc.id}>{doc.name}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  分配智能体
                </label>
                <select
                  value={newTask.assignedAgent || ''}
                  onChange={(e) => setNewTask(prev => ({ ...prev, assignedAgent: e.target.value || undefined }))}
                  className="input-field w-full"
                >
                  <option value="">自动分配</option>
                  {agents.map(agent => (
                    <option key={agent.id} value={agent.id}>{agent.name}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  任务描述
                </label>
                <textarea
                  value={newTask.description}
                  onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入任务描述..."
                  className="input-field w-full h-20 resize-none"
                />
              </div>
            </div>
            
            <div className="flex items-center justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowCreateForm(false)}
                className="btn-secondary"
              >
                取消
              </button>
              <button
                onClick={createTask}
                className="btn-primary"
                disabled={!newTask.description.trim()}
              >
                创建任务
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}