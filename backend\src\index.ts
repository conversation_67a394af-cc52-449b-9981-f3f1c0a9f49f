import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { documentRoutes } from './routes/documents';
import { agentRoutes } from './routes/agents';
import { a2aRoutes } from './routes/a2a';
import { aguiHandler } from './handlers/aguiHandler';
import { a2aHub } from './services/a2aHub';
import { mcpServer } from './services/mcpServer';

// 加载环境变量
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 8000;
const WS_PORT = process.env.WS_PORT || 8001;

// 中间件配置
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 路由配置
app.use('/api/documents', documentRoutes);
app.use('/api/agents', agentRoutes);
app.use('/api/a2a', a2aRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// WebSocket连接处理
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  // AGUI协议处理
  aguiHandler(socket, io);
  
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
  
  socket.on('error', (error) => {
    logger.error('Socket error:', error);
  });
});

// 错误处理中间件
app.use(errorHandler);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
});

// 启动服务器
server.listen(PORT, async () => {
  logger.info(`🚀 Backend server running on port ${PORT}`);
  logger.info(`📡 WebSocket server running on port ${WS_PORT}`);
  
  try {
    // A2A Hub已在构造函数中初始化
    logger.info('✅ A2A Hub initialized');
    
    // MCP服务器已在构造函数中初始化
    logger.info('✅ MCP Server initialized');
    
    logger.info('🎯 Multi-Agent Collaboration System ready!');
  } catch (error) {
    logger.error('❌ Failed to initialize services:', error);
    process.exit(1);
  }
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export { app, io };