import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { documentService } from './documentService';
import { a2aHub } from './a2aHub';

interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
  handler: (params: any) => Promise<any>;
}

interface MCPResource {
  uri: string;
  name: string;
  description: string;
  mimeType: string;
  handler: () => Promise<any>;
}

interface MCPPrompt {
  name: string;
  description: string;
  arguments?: any[];
  handler: (args: any) => Promise<string>;
}

interface MCPRequest {
  id: string;
  method: string;
  params?: any;
}

interface MCPResponse {
  id: string;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

class MCPServer extends EventEmitter {
  private tools: Map<string, MCPTool> = new Map();
  private resources: Map<string, MCPResource> = new Map();
  private prompts: Map<string, MCPPrompt> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    this.initializeServer();
  }

  // 初始化MCP服务器
  private async initializeServer(): Promise<void> {
    try {
      // 注册工具
      this.registerTools();
      
      // 注册资源
      this.registerResources();
      
      // 注册提示模板
      this.registerPrompts();
      
      this.isInitialized = true;
      logger.info('MCP Server initialized successfully');
      this.emit('initialized');
    } catch (error) {
      logger.error('Failed to initialize MCP Server:', error);
      throw error;
    }
  }

  // 注册工具
  private registerTools(): void {
    // 文档上传工具
    this.registerTool({
      name: 'upload_document',
      description: 'Upload and process a document',
      inputSchema: {
        type: 'object',
        properties: {
          filename: { type: 'string', description: 'Name of the file' },
          content: { type: 'string', description: 'Base64 encoded file content' },
          mimeType: { type: 'string', description: 'MIME type of the file' }
        },
        required: ['filename', 'content', 'mimeType']
      },
      handler: async (params) => {
        const { filename, content, mimeType } = params;
        
        // 解码base64内容
        const buffer = Buffer.from(content, 'base64');
        
        // 创建模拟的multer文件对象
        const file = {
          originalname: filename,
          buffer,
          size: buffer.length,
          mimetype: mimeType
        } as Express.Multer.File;
        
        // 保存文档
        const document = await documentService.saveDocument(file);
        
        // 开始处理
        await documentService.startDocumentProcessing(document.id);
        
        return {
          documentId: document.id,
          status: 'uploaded',
          message: 'Document uploaded and processing started'
        };
      }
    });

    // 获取文档状态工具
    this.registerTool({
      name: 'get_document_status',
      description: 'Get the processing status of a document',
      inputSchema: {
        type: 'object',
        properties: {
          documentId: { type: 'string', description: 'ID of the document' }
        },
        required: ['documentId']
      },
      handler: async (params) => {
        const { documentId } = params;
        const document = await documentService.getDocumentById(documentId);
        
        if (!document) {
          throw new Error(`Document not found: ${documentId}`);
        }
        
        const tasks = await documentService.getDocumentTasks(documentId);
        
        return {
          documentId,
          status: document.status,
          progress: document.progress,
          tasks: tasks.map(task => ({
            id: task.id,
            type: task.type,
            status: task.status,
            progress: task.progress
          })),
          results: document.processingResults
        };
      }
    });

    // 文档分析工具
    this.registerTool({
      name: 'analyze_document',
      description: 'Analyze a document with specific parameters',
      inputSchema: {
        type: 'object',
        properties: {
          documentId: { type: 'string', description: 'ID of the document' },
          analysisType: { 
            type: 'string', 
            enum: ['full', 'summary', 'entities', 'sentiment'],
            description: 'Type of analysis to perform'
          },
          parameters: { 
            type: 'object', 
            description: 'Additional parameters for analysis'
          }
        },
        required: ['documentId', 'analysisType']
      },
      handler: async (params) => {
        const { documentId, analysisType, parameters = {} } = params;
        
        // 创建分析任务
        const task = await documentService.createTask(documentId, 'analysis', {
          analysisType,
          ...parameters
        });
        
        // 分发任务
        await a2aHub.distributeTask({
          id: task.id,
          type: 'document_analysis',
          documentId,
          parameters: task.parameters,
          priority: 'normal'
        });
        
        return {
          taskId: task.id,
          status: 'started',
          message: 'Document analysis task created and distributed'
        };
      }
    });

    // 获取智能体状态工具
    this.registerTool({
      name: 'get_agents_status',
      description: 'Get the status of all registered agents',
      inputSchema: {
        type: 'object',
        properties: {}
      },
      handler: async () => {
        const agents = await a2aHub.getRegisteredAgents();
        
        return {
          agents: agents.map(agent => ({
            id: agent.id,
            name: agent.name,
            type: agent.type,
            status: agent.status,
            capabilities: agent.capabilities,
            lastHeartbeat: agent.lastHeartbeat,
            performance: agent.performance
          })),
          totalCount: agents.length,
          activeCount: agents.filter(agent => agent.status === 'active').length
        };
      }
    });

    // 创建任务工具
    this.registerTool({
      name: 'create_task',
      description: 'Create and distribute a new task',
      inputSchema: {
        type: 'object',
        properties: {
          type: { type: 'string', description: 'Type of task' },
          documentId: { type: 'string', description: 'ID of the document (if applicable)' },
          parameters: { type: 'object', description: 'Task parameters' },
          priority: { 
            type: 'string', 
            enum: ['low', 'normal', 'high', 'urgent'],
            default: 'normal',
            description: 'Task priority'
          }
        },
        required: ['type']
      },
      handler: async (params) => {
        const { type, documentId, parameters = {}, priority = 'normal' } = params;
        
        const task = await a2aHub.distributeTask({
          type,
          documentId,
          parameters,
          priority
        });
        
        return {
          taskId: task.id,
          assignedAgent: task.assignedAgent,
          status: task.status,
          estimatedTime: task.estimatedTime
        };
      }
    });

    // 文件系统操作工具
    this.registerTool({
      name: 'read_file',
      description: 'Read content from a file',
      inputSchema: {
        type: 'object',
        properties: {
          documentId: { type: 'string', description: 'ID of the document to read' }
        },
        required: ['documentId']
      },
      handler: async (params) => {
        const { documentId } = params;
        const content = await documentService.readDocumentContent(documentId);
        
        return {
          documentId,
          content,
          timestamp: new Date()
        };
      }
    });

    logger.info('MCP tools registered successfully');
  }

  // 注册资源
  private registerResources(): void {
    // 文档列表资源
    this.registerResource({
      uri: 'documents://list',
      name: 'Document List',
      description: 'List of all uploaded documents',
      mimeType: 'application/json',
      handler: async () => {
        const documents = await documentService.getAllDocuments();
        return {
          documents: documents.map(doc => ({
            id: doc.id,
            filename: doc.originalName,
            size: doc.size,
            status: doc.status,
            progress: doc.progress,
            uploadTime: doc.uploadTime
          })),
          count: documents.length
        };
      }
    });

    // 智能体状态资源
    this.registerResource({
      uri: 'agents://status',
      name: 'Agents Status',
      description: 'Current status of all agents',
      mimeType: 'application/json',
      handler: async () => {
        const agents = await a2aHub.getRegisteredAgents();
        return {
          agents,
          timestamp: new Date()
        };
      }
    });

    // 任务队列资源
    this.registerResource({
      uri: 'tasks://queue',
      name: 'Task Queue',
      description: 'Current task queue status',
      mimeType: 'application/json',
      handler: async () => {
        const tasks = await a2aHub.getAllTasks();
        return {
          tasks: tasks.map(task => ({
            id: task.id,
            type: task.type,
            status: task.status,
            assignedAgent: task.assignedAgent,
            createdAt: task.createdAt,
            priority: task.priority
          })),
          count: tasks.length
        };
      }
    });

    // 系统统计资源
    this.registerResource({
      uri: 'system://stats',
      name: 'System Statistics',
      description: 'Overall system statistics',
      mimeType: 'application/json',
      handler: async () => {
        const stats = await documentService.getProcessingStats();
        const agents = await a2aHub.getRegisteredAgents();
        const tasks = await a2aHub.getAllTasks();
        
        return {
          documents: stats.documents,
          tasks: stats.tasks,
          agents: {
            total: agents.length,
            active: agents.filter(a => a.status === 'active').length,
            busy: agents.filter(a => a.status === 'busy').length,
            inactive: agents.filter(a => a.status === 'inactive').length
          },
          uptime: process.uptime(),
          timestamp: new Date()
        };
      }
    });

    logger.info('MCP resources registered successfully');
  }

  // 注册提示模板
  private registerPrompts(): void {
    // 文档分析提示
    this.registerPrompt({
      name: 'document_analysis',
      description: 'Generate a prompt for document analysis',
      arguments: [
        { name: 'documentType', description: 'Type of document to analyze' },
        { name: 'analysisDepth', description: 'Depth of analysis (basic, detailed, comprehensive)' }
      ],
      handler: async (args) => {
        const { documentType = 'general', analysisDepth = 'detailed' } = args;
        
        const prompts = {
          basic: `请对这个${documentType}文档进行基础分析，包括主题识别和关键信息提取。`,
          detailed: `请对这个${documentType}文档进行详细分析，包括：
1. 文档主题和类型识别
2. 关键实体提取
3. 重要概念和术语识别
4. 文档结构分析
5. 内容摘要`,
          comprehensive: `请对这个${documentType}文档进行全面深度分析，包括：
1. 文档分类和主题识别
2. 命名实体识别（人名、地名、组织等）
3. 关键概念和专业术语提取
4. 文档结构和逻辑关系分析
5. 情感倾向和语调分析
6. 内容质量评估
7. 相关性和重要性评分
8. 详细摘要和关键点提取`
        };
        
        return prompts[analysisDepth] || prompts.detailed;
      }
    });

    // 摘要生成提示
    this.registerPrompt({
      name: 'document_summary',
      description: 'Generate a prompt for document summarization',
      arguments: [
        { name: 'length', description: 'Desired summary length (short, medium, long)' },
        { name: 'style', description: 'Summary style (bullet, paragraph, structured)' }
      ],
      handler: async (args) => {
        const { length = 'medium', style = 'paragraph' } = args;
        
        const lengthSpecs = {
          short: '100-200字',
          medium: '300-500字',
          long: '500-800字'
        };
        
        const styleSpecs = {
          bullet: '使用要点列表格式',
          paragraph: '使用段落格式',
          structured: '使用结构化格式，包含标题和子标题'
        };
        
        return `请为文档生成${lengthSpecs[length]}的摘要，${styleSpecs[style]}。摘要应该：
1. 准确反映文档的核心内容
2. 保持逻辑清晰和条理性
3. 突出重要信息和关键点
4. 使用简洁明了的语言`;
      }
    });

    // 实体提取提示
    this.registerPrompt({
      name: 'entity_extraction',
      description: 'Generate a prompt for entity extraction',
      arguments: [
        { name: 'entityTypes', description: 'Types of entities to extract' }
      ],
      handler: async (args) => {
        const { entityTypes = ['person', 'organization', 'location', 'date'] } = args;
        
        const typeDescriptions = {
          person: '人名',
          organization: '组织机构',
          location: '地理位置',
          date: '日期时间',
          product: '产品名称',
          event: '事件',
          concept: '概念术语'
        };
        
        const targetTypes = entityTypes.map(type => typeDescriptions[type] || type).join('、');
        
        return `请从文档中提取以下类型的命名实体：${targetTypes}。

要求：
1. 准确识别实体边界
2. 正确分类实体类型
3. 保持实体的完整性
4. 去除重复和无关信息
5. 按类型组织结果`;
      }
    });

    logger.info('MCP prompts registered successfully');
  }

  // 注册工具
  registerTool(tool: MCPTool): void {
    this.tools.set(tool.name, tool);
    logger.debug(`MCP tool registered: ${tool.name}`);
  }

  // 注册资源
  registerResource(resource: MCPResource): void {
    this.resources.set(resource.uri, resource);
    logger.debug(`MCP resource registered: ${resource.uri}`);
  }

  // 注册提示
  registerPrompt(prompt: MCPPrompt): void {
    this.prompts.set(prompt.name, prompt);
    logger.debug(`MCP prompt registered: ${prompt.name}`);
  }

  // 处理MCP请求
  async handleRequest(request: MCPRequest): Promise<MCPResponse> {
    try {
      let result;
      
      switch (request.method) {
        case 'tools/list':
          result = this.listTools();
          break;
        case 'tools/call':
          result = await this.callTool(request.params);
          break;
        case 'resources/list':
          result = this.listResources();
          break;
        case 'resources/read':
          result = await this.readResource(request.params);
          break;
        case 'prompts/list':
          result = this.listPrompts();
          break;
        case 'prompts/get':
          result = await this.getPrompt(request.params);
          break;
        case 'initialize':
          result = await this.initialize(request.params);
          break;
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }
      
      return {
        id: request.id,
        result
      };
    } catch (error) {
      logger.error('MCP request error:', error);
      return {
        id: request.id,
        error: {
          code: -1,
          message: error.message,
          data: error.stack
        }
      };
    }
  }

  // 列出所有工具
  private listTools(): any {
    return {
      tools: Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      }))
    };
  }

  // 调用工具
  private async callTool(params: any): Promise<any> {
    const { name, arguments: args } = params;
    
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }
    
    return await tool.handler(args || {});
  }

  // 列出所有资源
  private listResources(): any {
    return {
      resources: Array.from(this.resources.values()).map(resource => ({
        uri: resource.uri,
        name: resource.name,
        description: resource.description,
        mimeType: resource.mimeType
      }))
    };
  }

  // 读取资源
  private async readResource(params: any): Promise<any> {
    const { uri } = params;
    
    const resource = this.resources.get(uri);
    if (!resource) {
      throw new Error(`Resource not found: ${uri}`);
    }
    
    const contents = await resource.handler();
    
    return {
      contents: [{
        uri,
        mimeType: resource.mimeType,
        text: typeof contents === 'string' ? contents : JSON.stringify(contents, null, 2)
      }]
    };
  }

  // 列出所有提示
  private listPrompts(): any {
    return {
      prompts: Array.from(this.prompts.values()).map(prompt => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.arguments
      }))
    };
  }

  // 获取提示
  private async getPrompt(params: any): Promise<any> {
    const { name, arguments: args } = params;
    
    const prompt = this.prompts.get(name);
    if (!prompt) {
      throw new Error(`Prompt not found: ${name}`);
    }
    
    const content = await prompt.handler(args || {});
    
    return {
      description: prompt.description,
      messages: [
        {
          role: 'user',
          content: {
            type: 'text',
            text: content
          }
        }
      ]
    };
  }

  // 初始化
  private async initialize(params: any): Promise<any> {
    return {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {
          listChanged: true
        },
        resources: {
          subscribe: true,
          listChanged: true
        },
        prompts: {
          listChanged: true
        }
      },
      serverInfo: {
        name: 'Multi-Agent Collaboration MCP Server',
        version: '1.0.0',
        description: 'MCP server for multi-agent document processing system'
      }
    };
  }

  // 获取服务器状态
  getStatus(): any {
    return {
      initialized: this.isInitialized,
      toolsCount: this.tools.size,
      resourcesCount: this.resources.size,
      promptsCount: this.prompts.size,
      uptime: process.uptime()
    };
  }

  // 关闭服务器
  shutdown(): void {
    this.tools.clear();
    this.resources.clear();
    this.prompts.clear();
    this.isInitialized = false;
    
    logger.info('MCP Server shutdown completed');
  }
}

export const mcpServer = new MCPServer();
export { MCPTool, MCPResource, MCPPrompt, MCPRequest, MCPResponse };