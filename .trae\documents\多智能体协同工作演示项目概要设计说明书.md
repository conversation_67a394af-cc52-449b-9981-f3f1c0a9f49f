# 多智能体协同工作演示项目概要设计说明书

## 1. 项目概述

### 1.1 设计目标
本概要设计说明书基于已确认的需求分析文档，为多智能体协同工作演示项目提供完整的技术架构设计方案。项目旨在通过MCP、A2A、AGUI三个协议的集成，展示现代AI智能体生态系统的协作能力。

### 1.2 设计原则
- **模块化设计**：各组件职责清晰，便于独立开发和测试
- **协议标准化**：严格遵循MCP、A2A、AGUI协议规范
- **可扩展性**：架构支持新增智能体和功能模块
- **演示友好**：突出协议特性，便于教学和展示

## 2. 系统整体架构设计

### 2.1 架构层次

```mermaid
graph TB
    subgraph "前端层 (Frontend Layer)"
        UI["React Web界面"]
        AGUI["AGUI协议客户端"]
    end
    
    subgraph "网关层 (Gateway Layer)"
        Gateway["API网关"]
        WS["WebSocket服务"]
    end
    
    subgraph "协议层 (Protocol Layer)"
        A2A_Hub["A2A协议中心"]
        MCP_Server["MCP服务器"]
        AGUI_Server["AGUI服务器"]
    end
    
    subgraph "智能体层 (Agent Layer)"
        Coordinator["协调智能体"]
        Analyzer["文档分析智能体"]
        Processor["内容处理智能体"]
        UIAgent["界面交互智能体"]
    end
    
    subgraph "服务层 (Service Layer)"
        FileService["文件服务"]
        APIService["外部API服务"]
        DBService["数据库服务"]
    end
    
    UI --> AGUI
    AGUI --> Gateway
    Gateway --> WS
    WS --> AGUI_Server
    
    A2A_Hub --> Coordinator
    A2A_Hub --> Analyzer
    A2A_Hub --> Processor
    A2A_Hub --> UIAgent
    
    Analyzer --> MCP_Server
    Processor --> MCP_Server
    MCP_Server --> FileService
    MCP_Server --> APIService
    MCP_Server --> DBService
```

### 2.2 核心组件说明

#### 2.2.1 前端层
- **React Web界面**：用户交互的主要入口
- **AGUI协议客户端**：处理前端事件和状态管理

#### 2.2.2 网关层
- **API网关**：统一的API入口，负责路由和认证
- **WebSocket服务**：实现实时双向通信

#### 2.2.3 协议层
- **A2A协议中心**：智能体注册、发现和通信中心
- **MCP服务器**：提供工具和资源访问接口
- **AGUI服务器**：处理前端交互事件

#### 2.2.4 智能体层
- **协调智能体**：任务分发和结果整合
- **文档分析智能体**：文档内容分析和提取
- **内容处理智能体**：文本处理和转换
- **界面交互智能体**：前端状态管理

#### 2.2.5 服务层
- **文件服务**：文档存储和访问
- **外部API服务**：翻译、总结等第三方服务
- **数据库服务**：任务状态和结果存储

## 3. 技术选型和依赖管理

### 3.1 前端技术栈

| 技术组件 | 选型方案 | 版本要求 | 选择理由 |
|---------|---------|---------|----------|
| 前端框架 | React | ^18.0.0 | 生态成熟，组件化开发 |
| 类型系统 | TypeScript | ^5.0.0 | 类型安全，开发效率 |
| UI组件库 | Ant Design | ^5.0.0 | 组件丰富，设计规范 |
| 状态管理 | Redux Toolkit | ^1.9.0 | 状态管理标准化 |
| 构建工具 | Vite | ^4.0.0 | 快速构建，热更新 |
| 实时通信 | Socket.io-client | ^4.7.0 | WebSocket封装 |

### 3.2 后端技术栈

| 技术组件 | 选型方案 | 版本要求 | 选择理由 |
|---------|---------|---------|----------|
| 运行时 | Node.js | ^18.0.0 | JavaScript生态统一 |
| Web框架 | Express.js | ^4.18.0 | 轻量级，中间件丰富 |
| 实时通信 | Socket.io | ^4.7.0 | 双向通信支持 |
| 数据库 | SQLite | ^3.0.0 | 轻量级，易部署 |
| ORM框架 | Prisma | ^5.0.0 | 类型安全，迁移管理 |
| 文件处理 | Multer | ^1.4.0 | 文件上传处理 |

### 3.3 协议实现依赖

| 协议 | 实现库 | 版本要求 | 功能说明 |
|------|--------|---------|----------|
| MCP | @modelcontextprotocol/sdk | ^1.0.0 | MCP协议官方SDK |
| A2A | a2a-protocol | ^0.1.0 | 智能体间通信协议 |
| AGUI | agui-protocol | ^0.1.0 | 前端交互协议 |

### 3.4 智能体框架

| 组件 | 选型方案 | 版本要求 | 选择理由 |
|------|---------|---------|----------|
| AI框架 | LangChain | ^0.1.0 | 智能体开发标准 |
| 大模型接入 | 通义千问qwen-max | Latest | 阿里云大模型服务 |
| 向量数据库 | Chroma | ^0.4.0 | 轻量级向量存储 |

### 3.5 开发和部署工具

| 工具类型 | 选型方案 | 版本要求 | 用途说明 |
|---------|---------|---------|----------|
| 包管理器 | pnpm | ^8.0.0 | 快速安装，节省空间 |
| 代码规范 | ESLint + Prettier | Latest | 代码质量保证 |
| 容器化 | Docker | ^24.0.0 | 环境一致性 |
| 服务编排 | Docker Compose | ^2.0.0 | 多服务管理 |
| 进程管理 | PM2 | ^5.0.0 | 生产环境进程管理 |

## 4. MCP、A2A、AGUI协议集成架构

### 4.1 MCP协议集成架构

#### 4.1.1 MCP服务器设计
```typescript
// MCP服务器架构
interface MCPServer {
  // 工具注册和管理
  tools: {
    fileReader: FileReaderTool;
    apiCaller: APICallerTool;
    dataStorage: DataStorageTool;
  };
  
  // 资源管理
  resources: {
    documents: DocumentResource;
    cache: CacheResource;
  };
  
  // 客户端连接管理
  clients: MCPClient[];
}
```

#### 4.1.2 工具定义
- **文件读取工具**：读取上传的文档文件
- **API调用工具**：调用翻译、总结等外部API
- **数据存储工具**：存储处理结果和状态
- **缓存管理工具**：管理临时数据和缓存

### 4.2 A2A协议集成架构

#### 4.2.1 智能体注册中心
```typescript
// A2A协议中心设计
interface A2AHub {
  // 智能体注册表
  registry: Map<string, AgentInfo>;
  
  // 消息路由
  messageRouter: MessageRouter;
  
  // 任务协调
  taskCoordinator: TaskCoordinator;
  
  // 状态同步
  stateSync: StateSynchronizer;
}
```

#### 4.2.2 通信协议
- **智能体发现**：自动发现和注册机制
- **消息传递**：异步消息队列
- **任务分发**：基于能力的任务路由
- **状态同步**：实时状态广播

### 4.3 AGUI协议集成架构

#### 4.3.1 前端事件处理
```typescript
// AGUI协议客户端设计
interface AGUIClient {
  // 事件监听器
  eventListeners: Map<string, EventHandler>;
  
  // 状态管理器
  stateManager: StateManager;
  
  // 组件更新器
  componentUpdater: ComponentUpdater;
  
  // WebSocket连接
  connection: WebSocketConnection;
}
```

#### 4.3.2 交互事件
- **文件上传事件**：处理文档上传
- **进度更新事件**：实时进度展示
- **结果展示事件**：处理结果呈现
- **错误处理事件**：异常情况处理

## 5. 智能体设计和交互流程

### 5.1 智能体详细设计

#### 5.1.1 协调智能体（Coordinator Agent）
```typescript
class CoordinatorAgent {
  // 核心能力
  capabilities = {
    taskDecomposition: true,
    resultAggregation: true,
    progressTracking: true,
    errorHandling: true
  };
  
  // 主要方法
  async processDocument(file: File): Promise<ProcessResult> {
    // 1. 任务分解
    const tasks = this.decomposeTask(file);
    
    // 2. 任务分发
    const results = await this.distributeTask(tasks);
    
    // 3. 结果聚合
    return this.aggregateResults(results);
  }
}
```

#### 5.1.2 文档分析智能体（Analyzer Agent）
```typescript
class AnalyzerAgent {
  // 核心能力
  capabilities = {
    documentParsing: true,
    contentExtraction: true,
    keywordAnalysis: true,
    structureAnalysis: true
  };
  
  // MCP工具集成
  mcpTools = {
    fileReader: new FileReaderTool(),
    textExtractor: new TextExtractorTool()
  };
}
```

#### 5.1.3 内容处理智能体（Processor Agent）
```typescript
class ProcessorAgent {
  // 核心能力
  capabilities = {
    textSummarization: true,
    languageTranslation: true,
    formatConversion: true,
    contentOptimization: true
  };
  
  // MCP工具集成
  mcpTools = {
    translationAPI: new TranslationAPITool(),
    summarizationAPI: new SummarizationAPITool()
  };
}
```

#### 5.1.4 界面交互智能体（UI Agent）
```typescript
class UIAgent {
  // 核心能力
  capabilities = {
    stateManagement: true,
    progressUpdating: true,
    resultPresentation: true,
    userInteraction: true
  };
  
  // AGUI协议集成
  aguiClient: AGUIClient;
}
```

### 5.2 智能体交互流程

#### 5.2.1 完整工作流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as UI Agent
    participant Coord as Coordinator
    participant Analyzer as Analyzer
    participant Processor as Processor
    participant MCP as MCP Server
    
    User->>UI: 上传文档
    UI->>Coord: 发送处理请求
    
    Coord->>Analyzer: 分配分析任务
    Analyzer->>MCP: 调用文件读取工具
    MCP-->>Analyzer: 返回文档内容
    Analyzer-->>Coord: 返回分析结果
    
    Coord->>Processor: 分配处理任务
    Processor->>MCP: 调用翻译API工具
    MCP-->>Processor: 返回翻译结果
    Processor-->>Coord: 返回处理结果
    
    Coord->>UI: 发送最终结果
    UI->>User: 展示处理结果
```

#### 5.2.2 协议交互细节

**A2A协议交互**：
1. 智能体注册和发现
2. 任务消息传递
3. 状态同步更新
4. 结果数据传递

**MCP协议交互**：
1. 工具能力查询
2. 工具调用请求
3. 资源访问操作
4. 结果数据返回

**AGUI协议交互**：
1. 前端事件捕获
2. 状态变更通知
3. 组件更新指令
4. 用户反馈处理

## 6. 数据流和控制流设计

### 6.1 数据流设计

#### 6.1.1 数据流向图
```mermaid
flowchart TD
    A["用户上传文档"] --> B["文件存储"]
    B --> C["文档分析"]
    C --> D["内容提取"]
    D --> E["处理任务分发"]
    E --> F["翻译处理"]
    E --> G["摘要生成"]
    F --> H["结果聚合"]
    G --> H
    H --> I["结果存储"]
    I --> J["前端展示"]
    
    K["实时状态"] --> L["进度更新"]
    L --> M["界面刷新"]
```

#### 6.1.2 数据模型设计

**文档数据模型**：
```typescript
interface DocumentData {
  id: string;
  filename: string;
  content: string;
  metadata: {
    size: number;
    type: string;
    uploadTime: Date;
  };
  status: 'uploaded' | 'processing' | 'completed' | 'error';
}
```

**任务数据模型**：
```typescript
interface TaskData {
  id: string;
  type: 'analyze' | 'translate' | 'summarize';
  documentId: string;
  agentId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  result?: any;
  error?: string;
}
```

**处理结果模型**：
```typescript
interface ProcessResult {
  documentId: string;
  analysis: {
    keywords: string[];
    summary: string;
    language: string;
  };
  translation?: {
    targetLanguage: string;
    translatedText: string;
  };
  processing: {
    startTime: Date;
    endTime: Date;
    duration: number;
  };
}
```

### 6.2 控制流设计

#### 6.2.1 主控制流程
```typescript
// 主控制流程伪代码
async function mainControlFlow(document: File) {
  try {
    // 1. 初始化阶段
    const taskId = await initializeTask(document);
    
    // 2. 文档上传阶段
    await uploadDocument(document, taskId);
    
    // 3. 任务分发阶段
    const subtasks = await distributeTask(taskId);
    
    // 4. 并行处理阶段
    const results = await Promise.all([
      analyzeDocument(subtasks.analyze),
      processContent(subtasks.process)
    ]);
    
    // 5. 结果聚合阶段
    const finalResult = await aggregateResults(results);
    
    // 6. 结果展示阶段
    await presentResults(finalResult);
    
  } catch (error) {
    await handleError(error);
  }
}
```

#### 6.2.2 错误处理流程
```typescript
// 错误处理策略
interface ErrorHandlingStrategy {
  // 重试机制
  retry: {
    maxAttempts: number;
    backoffStrategy: 'linear' | 'exponential';
  };
  
  // 降级策略
  fallback: {
    enableFallback: boolean;
    fallbackMethod: string;
  };
  
  // 用户通知
  notification: {
    showError: boolean;
    errorMessage: string;
  };
}
```

## 7. 接口设计和通信协议

### 7.1 REST API接口设计

#### 7.1.1 文档管理接口
```typescript
// 文档上传接口
POST /api/documents/upload
Content-Type: multipart/form-data
Request: {
  file: File;
  metadata?: DocumentMetadata;
}
Response: {
  documentId: string;
  status: string;
  message: string;
}

// 文档状态查询接口
GET /api/documents/{documentId}/status
Response: {
  documentId: string;
  status: DocumentStatus;
  progress: number;
  result?: ProcessResult;
}

// 处理结果获取接口
GET /api/documents/{documentId}/result
Response: {
  documentId: string;
  result: ProcessResult;
  downloadUrl?: string;
}
```

#### 7.1.2 智能体管理接口
```typescript
// 智能体状态查询
GET /api/agents/status
Response: {
  agents: AgentInfo[];
  totalCount: number;
  activeCount: number;
}

// 任务分发接口
POST /api/agents/tasks
Request: {
  taskType: string;
  documentId: string;
  parameters: any;
}
Response: {
  taskId: string;
  assignedAgent: string;
  estimatedTime: number;
}
```

### 7.2 WebSocket通信协议

#### 7.2.1 实时事件定义
```typescript
// WebSocket事件类型
type WebSocketEvent = 
  | 'document.upload.progress'
  | 'task.status.update'
  | 'agent.status.change'
  | 'result.ready'
  | 'error.occurred';

// 事件数据格式
interface WebSocketMessage {
  event: WebSocketEvent;
  data: any;
  timestamp: Date;
  sessionId: string;
}
```

#### 7.2.2 协议消息格式

**MCP协议消息**：
```typescript
interface MCPMessage {
  jsonrpc: '2.0';
  method: string;
  params?: any;
  id?: string | number;
}
```

**A2A协议消息**：
```typescript
interface A2AMessage {
  from: string;
  to: string;
  type: 'request' | 'response' | 'notification';
  payload: any;
  timestamp: Date;
}
```

**AGUI协议消息**：
```typescript
interface AGUIMessage {
  type: 'event' | 'state' | 'command';
  component: string;
  action: string;
  data: any;
}
```

### 7.3 数据库接口设计

#### 7.3.1 数据库表结构
```sql
-- 文档表
CREATE TABLE documents (
  id VARCHAR(36) PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  content_type VARCHAR(100),
  file_size INTEGER,
  file_path VARCHAR(500),
  status VARCHAR(20) DEFAULT 'uploaded',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
  id VARCHAR(36) PRIMARY KEY,
  document_id VARCHAR(36) REFERENCES documents(id),
  agent_id VARCHAR(100),
  task_type VARCHAR(50),
  status VARCHAR(20) DEFAULT 'pending',
  progress INTEGER DEFAULT 0,
  result TEXT,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);

-- 智能体表
CREATE TABLE agents (
  id VARCHAR(100) PRIMARY KEY,
  name VARCHAR(100),
  type VARCHAR(50),
  capabilities TEXT,
  status VARCHAR(20) DEFAULT 'active',
  last_heartbeat TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 7.3.2 数据访问层设计
```typescript
// 数据访问接口
interface DocumentRepository {
  create(document: DocumentData): Promise<string>;
  findById(id: string): Promise<DocumentData | null>;
  updateStatus(id: string, status: string): Promise<void>;
  delete(id: string): Promise<void>;
}

interface TaskRepository {
  create(task: TaskData): Promise<string>;
  findByDocumentId(documentId: string): Promise<TaskData[]>;
  updateProgress(id: string, progress: number): Promise<void>;
  updateResult(id: string, result: any): Promise<void>;
}
```

## 8. 部署架构和环境配置

### 8.1 完整容器化部署架构

#### 8.1.1 Docker容器架构设计

##### 容器服务组成
- **前端容器**：nginx:alpine + React构建产物
- **后端容器**：node:18-alpine + Express应用
- **智能体容器集群**：4个独立的node:18-alpine微服务
- **MCP工具容器**：node:18-alpine + MCP服务器
- **Redis容器**：redis:7-alpine（会话和缓存存储）
- **数据库容器**：postgres:15-alpine（持久化数据存储）

##### 项目目录结构
```
project-root/
├── docker-compose.yml          # 主编排文件
├── docker-compose.dev.yml      # 开发环境配置
├── docker-compose.prod.yml     # 生产环境配置
├── .env.example                # 环境变量模板
├── frontend/
│   ├── Dockerfile              # 前端容器配置
│   ├── nginx.conf              # Nginx配置文件
│   └── .dockerignore           # Docker忽略文件
├── backend/
│   ├── Dockerfile              # 后端容器配置
│   └── .dockerignore           # Docker忽略文件
├── agents/
│   ├── coordinator/
│   │   └── Dockerfile          # 协调智能体容器
│   ├── document-analyzer/
│   │   └── Dockerfile          # 文档分析智能体容器
│   ├── content-processor/
│   │   └── Dockerfile          # 内容处理智能体容器
│   └── ui-interaction/
│       └── Dockerfile          # 界面交互智能体容器
├── mcp-tools/
│   ├── Dockerfile              # MCP工具服务器容器
│   └── tools-config.json       # 工具配置文件
├── scripts/
│   ├── build.sh                # 构建脚本
│   ├── deploy.sh               # 部署脚本
│   └── cleanup.sh              # 清理脚本
└── docs/
    ├── deployment.md           # 部署文档
    └── docker-guide.md         # Docker使用指南
```

#### 8.1.2 主要Docker配置文件

##### docker-compose.yml（生产环境）
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://backend:8000
      - REACT_APP_WS_URL=ws://backend:8001
    depends_on:
      - backend
    networks:
      - multi-agent-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
      - "8001:8001"  # WebSocket端口
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/multiagent
      - REDIS_URL=redis://redis:6379
      - QWEN_API_KEY=${QWEN_API_KEY}
      - QWEN_BASE_URL=${QWEN_BASE_URL}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - multi-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=multiagent
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - multi-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - multi-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # 协调智能体
  coordinator-agent:
    build:
      context: ./agents/coordinator
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - QWEN_API_KEY=${QWEN_API_KEY}
      - QWEN_BASE_URL=${QWEN_BASE_URL}
      - REDIS_URL=redis://redis:6379
      - A2A_HUB_URL=http://backend:8000/a2a
    depends_on:
      - redis
      - backend
    networks:
      - multi-agent-network
    restart: unless-stopped
    deploy:
      replicas: 1

  # 文档分析智能体
  document-analyzer:
    build:
      context: ./agents/document-analyzer
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - QWEN_API_KEY=${QWEN_API_KEY}
      - QWEN_BASE_URL=${QWEN_BASE_URL}
      - MCP_SERVER_URL=http://mcp-tools:9000
    depends_on:
      - mcp-tools
    networks:
      - multi-agent-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # 内容处理智能体
  content-processor:
    build:
      context: ./agents/content-processor
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - QWEN_API_KEY=${QWEN_API_KEY}
      - QWEN_BASE_URL=${QWEN_BASE_URL}
      - MCP_SERVER_URL=http://mcp-tools:9000
    depends_on:
      - mcp-tools
    networks:
      - multi-agent-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # 界面交互智能体
  ui-interaction:
    build:
      context: ./agents/ui-interaction
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - QWEN_API_KEY=${QWEN_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - multi-agent-network
    restart: unless-stopped
    deploy:
      replicas: 1

  # MCP工具服务器
  mcp-tools:
    build:
      context: ./mcp-tools
      dockerfile: Dockerfile
    ports:
      - "9000:9000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./uploads:/app/uploads:ro
      - ./shared-data:/app/data
    networks:
      - multi-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres-data:
  redis-data:

networks:
  multi-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

##### docker-compose.dev.yml（开发环境）
```yaml
version: '3.8'

services:
  frontend:
    build:
      target: development
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8001
    command: npm run dev

  backend:
    volumes:
      - ./backend/src:/app/src
      - ./backend/package.json:/app/package.json
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
    command: npm run dev

  # 开发环境下的其他服务配置...
```

#### 8.1.3 详细Dockerfile配置

##### 前端Dockerfile（多阶段构建）
```dockerfile
# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码并构建
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine AS production

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# 开发阶段
FROM node:18-alpine AS development
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]
```

##### 后端Dockerfile
```dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    dumb-init

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制源代码
COPY --chown=nextjs:nodejs . .

# 创建必要目录
RUN mkdir -p /app/uploads /app/logs && \
    chown -R nextjs:nodejs /app

# 切换到非root用户
USER nextjs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000 8001

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
```

##### 智能体Dockerfile模板
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache curl dumb-init

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S agent -u 1001

# 复制源代码
COPY --chown=agent:nodejs . .

# 切换到非root用户
USER agent

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
```

##### MCP工具服务器Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装系统依赖（包括文档处理工具）
RUN apk add --no-cache \
    curl \
    dumb-init \
    python3 \
    py3-pip \
    poppler-utils \
    imagemagick

# 安装Python依赖（用于文档处理）
RUN pip3 install --no-cache-dir \
    PyPDF2 \
    python-docx \
    openpyxl

# 复制Node.js依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcp -u 1001

# 复制源代码
COPY --chown=mcp:nodejs . .

# 创建数据目录
RUN mkdir -p /app/data /app/uploads && \
    chown -R mcp:nodejs /app

USER mcp

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9000/health || exit 1

EXPOSE 9000

ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
```

### 8.2 通义千问qwen-max模型集成方案

#### 8.2.1 qwen-max API集成架构

##### API客户端封装
```typescript
// qwen-client.ts
import axios, { AxiosInstance } from 'axios';

interface QwenConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  timeout: number;
}

interface QwenMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface QwenResponse {
  output: {
    text: string;
    finish_reason: string;
  };
  usage: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
  };
  request_id: string;
}

class QwenClient {
  private client: AxiosInstance;
  private config: QwenConfig;

  constructor(config: QwenConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'X-DashScope-SSE': 'disable'
      }
    });
  }

  async chat(
    messages: QwenMessage[], 
    options: {
      temperature?: number;
      top_p?: number;
      max_tokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<QwenResponse> {
    try {
      const response = await this.client.post('/services/aigc/text-generation/generation', {
        model: this.config.model,
        input: {
          messages: messages
        },
        parameters: {
          temperature: options.temperature || 0.7,
          top_p: options.top_p || 0.8,
          max_tokens: options.max_tokens || 2000,
          stream: options.stream || false,
          result_format: 'message'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Qwen API调用失败:', error);
      throw new Error(`Qwen API调用失败: ${error.message}`);
    }
  }

  async streamChat(
    messages: QwenMessage[],
    onChunk: (chunk: string) => void,
    options: any = {}
  ): Promise<void> {
    try {
      const response = await this.client.post('/services/aigc/text-generation/generation', {
        model: this.config.model,
        input: { messages },
        parameters: {
          ...options,
          stream: true
        }
      }, {
        responseType: 'stream'
      });

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.output?.text) {
                onChunk(data.output.text);
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });
    } catch (error) {
      throw new Error(`Qwen流式调用失败: ${error.message}`);
    }
  }
}

export { QwenClient, QwenConfig, QwenMessage, QwenResponse };
```

##### 智能体qwen-max集成实现

**协调智能体实现**：
```typescript
// coordinator-agent.ts
import { QwenClient } from '../utils/qwen-client';

class CoordinatorAgent {
  private qwenClient: QwenClient;
  private systemPrompt: string;

  constructor() {
    this.qwenClient = new QwenClient({
      apiKey: process.env.QWEN_API_KEY!,
      baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
      model: 'qwen-max',
      timeout: 30000
    });

    this.systemPrompt = `你是一个协调智能体，负责任务分解和流程管理。
你的职责包括：
1. 分析用户上传的文档类型和处理需求
2. 将复杂任务分解为可执行的子任务
3. 协调其他智能体完成任务
4. 整合处理结果并反馈给用户
5. 监控任务执行进度和处理异常情况

请始终以JSON格式返回任务分解结果。`;
  }

  async processTask(userInput: string, documentInfo: any): Promise<any> {
    const messages = [
      { role: 'system' as const, content: this.systemPrompt },
      { 
        role: 'user' as const, 
        content: `用户请求：${userInput}\n文档信息：${JSON.stringify(documentInfo)}\n请分解任务并制定执行计划。` 
      }
    ];

    try {
      const response = await this.qwenClient.chat(messages, {
        temperature: 0.3,
        max_tokens: 1500
      });

      return this.parseCoordinationPlan(response.output.text);
    } catch (error) {
      console.error('协调智能体处理失败:', error);
      throw error;
    }
  }

  private parseCoordinationPlan(response: string): any {
    try {
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // 如果不是JSON格式，返回结构化数据
      return {
        tasks: [
          { type: 'analyze', priority: 1, description: '文档分析' },
          { type: 'process', priority: 2, description: '内容处理' }
        ],
        plan: response
      };
    } catch (error) {
      console.error('解析协调计划失败:', error);
      return { error: '解析失败', raw: response };
    }
  }
}

export { CoordinatorAgent };
```

**文档分析智能体实现**：
```typescript
// document-analyzer-agent.ts
import { QwenClient } from '../utils/qwen-client';

class DocumentAnalyzerAgent {
  private qwenClient: QwenClient;
  private systemPrompt: string;

  constructor() {
    this.qwenClient = new QwenClient({
      apiKey: process.env.QWEN_API_KEY!,
      baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
      model: 'qwen-max',
      timeout: 45000
    });

    this.systemPrompt = `你是一个文档分析专家，擅长：
1. 识别文档类型（PDF、Word、Excel、图片等）
2. 提取文档结构和关键信息
3. 分析文档内容的主题和要点
4. 生成结构化的分析报告
5. 识别文档中的表格、图表和特殊格式

请以JSON格式返回分析结果，包含：summary、keywords、structure、content_type等字段。`;
  }

  async analyzeDocument(documentContent: string, metadata: any): Promise<any> {
    const messages = [
      { role: 'system' as const, content: this.systemPrompt },
      { 
        role: 'user' as const, 
        content: `请分析以下文档：\n\n文档元数据：${JSON.stringify(metadata)}\n\n文档内容：\n${documentContent.substring(0, 8000)}` 
      }
    ];

    try {
      const response = await this.qwenClient.chat(messages, {
        temperature: 0.2,
        max_tokens: 2000
      });

      return this.formatAnalysisResult(response.output.text);
    } catch (error) {
      console.error('文档分析失败:', error);
      throw error;
    }
  }

  private formatAnalysisResult(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return {
        summary: response.substring(0, 500),
        keywords: [],
        structure: 'unknown',
        content_type: 'text',
        raw_analysis: response
      };
    } catch (error) {
      return {
        error: '分析结果解析失败',
        raw: response
      };
    }
  }
}

export { DocumentAnalyzerAgent };
```

#### 8.2.2 qwen-max配置和优化

##### 环境变量配置
```bash
# .env.example
# 基础配置
NODE_ENV=development
PORT=8000

# 数据库配置
DATABASE_URL=********************************************/multiagent
REDIS_URL=redis://redis:6379

# 通义千问qwen-max配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-max
QWEN_TIMEOUT=30000
QWEN_MAX_RETRIES=3
QWEN_RETRY_DELAY=1000

# qwen-max参数配置
QWEN_DEFAULT_TEMPERATURE=0.7
QWEN_DEFAULT_TOP_P=0.8
QWEN_DEFAULT_MAX_TOKENS=2000
QWEN_ENABLE_STREAM=true

# 服务地址
A2A_HUB_URL=http://backend:8000/a2a
MCP_SERVER_URL=http://mcp-tools:9000
AGUI_SERVER_URL=http://backend:8000/agui

# WebSocket配置
WS_PORT=8001
WS_CORS_ORIGIN=http://frontend:3000

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,xlsx,pptx

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 安全配置
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000

# 监控配置
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
```

##### qwen-max错误处理和重试机制
```typescript
// qwen-error-handler.ts
class QwenErrorHandler {
  static async withRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        
        // 检查是否应该重试
        if (!this.shouldRetry(error) || attempt === maxRetries - 1) {
          throw error;
        }
        
        // 指数退避策略
        const delay = baseDelay * Math.pow(2, attempt);
        await this.sleep(delay);
        
        console.warn(`Qwen API调用失败，第${attempt + 1}次重试，${delay}ms后重试:`, error.message);
      }
    }
    
    throw lastError!;
  }
  
  private static shouldRetry(error: any): boolean {
    // 网络错误或服务器错误可以重试
    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      return true;
    }
    
    // HTTP状态码5xx可以重试
    if (error.response?.status >= 500) {
      return true;
    }
    
    // 限流错误可以重试
    if (error.response?.status === 429) {
      return true;
    }
    
    return false;
  }
  
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// qwen-usage-monitor.ts
class QwenUsageMonitor {
  private dailyTokenLimit: number;
  private currentUsage: number;
  private usageHistory: Map<string, number>;
  
  constructor(dailyLimit: number = 100000) {
    this.dailyTokenLimit = dailyLimit;
    this.currentUsage = 0;
    this.usageHistory = new Map();
  }
  
  async trackUsage(response: any): Promise<void> {
    const tokens = response.usage?.total_tokens || 0;
    const today = new Date().toISOString().split('T')[0];
    
    this.currentUsage += tokens;
    this.usageHistory.set(today, (this.usageHistory.get(today) || 0) + tokens);
    
    // 记录使用情况
    console.log(`Token使用: ${tokens}, 今日总计: ${this.currentUsage}`);
    
    // 接近限制时发出警告
    if (this.currentUsage > this.dailyTokenLimit * 0.8) {
      console.warn(`⚠️  Token使用量接近每日限制: ${this.currentUsage}/${this.dailyTokenLimit}`);
    }
    
    // 超过限制时抛出错误
    if (this.currentUsage > this.dailyTokenLimit) {
      throw new Error('已超过每日Token使用限制');
    }
  }
  
  getUsageStats(): any {
    return {
      current: this.currentUsage,
      limit: this.dailyTokenLimit,
      percentage: (this.currentUsage / this.dailyTokenLimit) * 100,
      history: Object.fromEntries(this.usageHistory)
    };
  }
}

export { QwenErrorHandler, QwenUsageMonitor };
```

#### 8.2.3 配置管理和部署脚本

##### 配置管理策略
```typescript
// config-manager.ts
interface AppConfig {
  server: {
    port: number;
    host: string;
  };
  database: {
    url: string;
  };
  redis: {
    url: string;
  };
  qwen: {
    apiKey: string;
    baseURL: string;
    model: string;
    timeout: number;
    maxRetries: number;
    retryDelay: number;
  };
  protocols: {
    a2a: {
      hubUrl: string;
    };
    mcp: {
      serverUrl: string;
    };
  };
}

class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;

  constructor() {
    this.config = {
      server: {
        port: parseInt(process.env.PORT || '8000'),
        host: process.env.HOST || '0.0.0.0'
      },
      database: {
        url: process.env.DATABASE_URL || '********************************************/multiagent'
      },
      redis: {
        url: process.env.REDIS_URL || 'redis://redis:6379'
      },
      qwen: {
        apiKey: process.env.QWEN_API_KEY || '',
        baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/api/v1',
        model: process.env.QWEN_MODEL || 'qwen-max',
        timeout: parseInt(process.env.QWEN_TIMEOUT || '30000'),
        maxRetries: parseInt(process.env.QWEN_MAX_RETRIES || '3'),
        retryDelay: parseInt(process.env.QWEN_RETRY_DELAY || '1000')
      },
      protocols: {
        a2a: {
          hubUrl: process.env.A2A_HUB_URL || 'http://backend:8000/a2a'
        },
        mcp: {
          serverUrl: process.env.MCP_SERVER_URL || 'http://mcp-tools:9000'
        }
      }
    };
    
    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.config.qwen.apiKey) {
      throw new Error('QWEN_API_KEY环境变量未设置');
    }
    
    if (!this.config.database.url) {
      throw new Error('DATABASE_URL环境变量未设置');
    }
  }

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  getConfig(): AppConfig {
    return this.config;
  }
}

export { ConfigManager, AppConfig };
```

##### 部署脚本

**构建脚本（build.sh）**：
```bash
#!/bin/bash

set -e

echo "🚀 开始构建多智能体协同工作演示项目..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  .env文件不存在，从模板复制..."
    cp .env.example .env
    echo "📝 请编辑.env文件，设置必要的环境变量（特别是QWEN_API_KEY）"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads logs shared-data

# 构建所有镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 检查镜像是否构建成功
echo "✅ 检查构建的镜像..."
docker images | grep multi-agent

echo "🎉 构建完成！"
echo "💡 运行 './deploy.sh' 来启动服务"
```

**部署脚本（deploy.sh）**：
```bash
#!/bin/bash

set -e

echo "🚀 开始部署多智能体协同工作演示项目..."

# 检查.env文件
if [ ! -f .env ]; then
    echo "❌ .env文件不存在，请先运行 './build.sh'"
    exit 1
fi

# 检查必要的环境变量
source .env
if [ -z "$QWEN_API_KEY" ]; then
    echo "❌ QWEN_API_KEY未设置，请在.env文件中设置"
    exit 1
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down --remove-orphans

# 清理旧的数据（可选）
read -p "是否清理旧数据？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧数据..."
    docker volume prune -f
    rm -rf uploads/* logs/* shared-data/*
fi

# 启动数据库服务
echo "🗄️  启动数据库服务..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 运行数据库迁移
echo "📊 运行数据库迁移..."
docker-compose run --rm backend npm run migrate

# 启动所有服务
echo "🚀 启动所有服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..5}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端服务健康检查通过"
        break
    else
        echo "⏳ 等待后端服务启动... ($i/5)"
        sleep 5
    fi
done

for i in {1..5}; do
    if curl -f http://localhost:9000/health > /dev/null 2>&1; then
        echo "✅ MCP服务健康检查通过"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 5
    fi
done

echo "🎉 部署完成！"
echo "🌐 前端访问地址: http://localhost:3000"
echo "🔧 后端API地址: http://localhost:8000"
echo "🛠️  MCP工具地址: http://localhost:9000"
echo "📊 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
```

**清理脚本（cleanup.sh）**：
```bash
#!/bin/bash

set -e

echo "🧹 清理多智能体协同工作演示项目..."

# 停止所有服务
echo "🛑 停止所有服务..."
docker-compose down --remove-orphans

# 删除所有镜像
read -p "是否删除所有项目镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除项目镜像..."
    docker images | grep multi-agent | awk '{print $3}' | xargs -r docker rmi -f
fi

# 删除所有卷
read -p "是否删除所有数据卷？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除数据卷..."
    docker-compose down -v
    docker volume prune -f
fi

# 清理本地文件
read -p "是否清理本地文件？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理本地文件..."
    rm -rf uploads/* logs/* shared-data/*
fi

echo "✅ 清理完成！"
```

#### 8.2.4 监控和日志配置

##### 日志配置
```typescript
// logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'multi-agent-demo' },
  transports: [
    // 写入所有日志到 combined.log
    new winston.transports.File({ 
      filename: '/app/logs/combined.log',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
    // 写入错误日志到 error.log
    new winston.transports.File({ 
      filename: '/app/logs/error.log', 
      level: 'error',
      maxsize: 10485760,
      maxFiles: 5
    }),
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// 生产环境不输出到控制台
if (process.env.NODE_ENV === 'production') {
  logger.remove(logger.transports[2]);
}

export default logger;
```

##### 健康检查端点
```typescript
// health.ts
import { Request, Response } from 'express';
import { ConfigManager } from './config-manager';
import logger from './logger';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  services: {
    database: 'up' | 'down';
    redis: 'up' | 'down';
    qwen: 'up' | 'down';
    mcp: 'up' | 'down';
  };
  version: string;
  uptime: number;
}

class HealthChecker {
  private config = ConfigManager.getInstance().getConfig();

  async checkHealth(): Promise<HealthStatus> {
    const status: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'down',
        redis: 'down',
        qwen: 'down',
        mcp: 'down'
      },
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime()
    };

    try {
      // 检查数据库连接
      status.services.database = await this.checkDatabase() ? 'up' : 'down';
      
      // 检查Redis连接
      status.services.redis = await this.checkRedis() ? 'up' : 'down';
      
      // 检查Qwen API
      status.services.qwen = await this.checkQwen() ? 'up' : 'down';
      
      // 检查MCP服务
      status.services.mcp = await this.checkMCP() ? 'up' : 'down';
      
      // 如果任何服务不可用，标记为不健康
      const allServicesUp = Object.values(status.services).every(s => s === 'up');
      status.status = allServicesUp ? 'healthy' : 'unhealthy';
      
    } catch (error) {
      logger.error('健康检查失败:', error);
      status.status = 'unhealthy';
    }

    return status;
  }

  private async checkDatabase(): Promise<boolean> {
    try {
      // 这里应该实现实际的数据库连接检查
      return true;
    } catch {
      return false;
    }
  }

  private async checkRedis(): Promise<boolean> {
    try {
      // 这里应该实现实际的Redis连接检查
      return true;
    } catch {
      return false;
    }
  }

  private async checkQwen(): Promise<boolean> {
    try {
      // 简单的API可用性检查
      const response = await fetch(`${this.config.qwen.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.qwen.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async checkMCP(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.protocols.mcp.serverUrl}/health`, {
        timeout: 5000
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

export const healthChecker = new HealthChecker();

export const healthEndpoint = async (req: Request, res: Response) => {
  try {
    const health = await healthChecker.checkHealth();
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('健康检查端点错误:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: 'Internal server error'
    });
  }
};
```

##### Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'multi-agent-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'multi-agent-mcp'
    static_configs:
      - targets: ['mcp-tools:9000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'multi-agent-agents'
    static_configs:
      - targets: ['agent-coordinator:8001', 'agent-analyzer:8002', 'agent-processor:8003']
    metrics_path: '/metrics'
    scrape_interval: 10s
```

## 9. 项目总结

### 9.1 技术亮点

1. **协议原生集成**：深度集成MCP、A2A、AGUI三大协议，展示真正的多协议协同工作
2. **容器化架构**：完整的Docker容器化部署方案，支持开发和生产环境
3. **qwen-max集成**：针对通义千问qwen-max模型的专门优化和集成
4. **智能体协作**：四个专业智能体的协同工作模式
5. **实时交互**：基于WebSocket的实时用户界面更新
6. **工具生态**：丰富的MCP工具集成，支持文档处理、数据分析等
7. **监控运维**：完整的日志、监控和健康检查体系

### 9.2 核心价值

- **技术演示**：展示多智能体协同工作的完整技术栈
- **协议集成**：验证MCP、A2A、AGUI协议的实际应用价值
- **开发参考**：为类似项目提供完整的架构和实现参考
- **扩展基础**：为更复杂的多智能体应用提供基础框架

### 9.3 部署指南

1. **环境准备**：
   - 安装Docker和Docker Compose
   - 获取通义千问API密钥
   - 配置环境变量

2. **快速启动**：
   ```bash
   # 克隆项目
   git clone <project-repo>
   cd multi-agent-demo
   
   # 构建和部署
   chmod +x build.sh deploy.sh cleanup.sh
   ./build.sh
   ./deploy.sh
   ```

3. **访问应用**：
   - 前端界面：http://localhost:3000
   - 后端API：http://localhost:8000
   - MCP工具：http://localhost:9000

### 9.4 扩展方向

1. **更多智能体**：添加专门的数据可视化、报告生成智能体
2. **更多协议**：集成更多的智能体通信协议
3. **更多工具**：扩展MCP工具集，支持更多文档格式和分析功能
4. **性能优化**：添加缓存、负载均衡等性能优化措施
5. **安全增强**：添加身份认证、权限控制等安全功能

---

**项目完成时间**：预计4-6周  
**技术难度**：中高级  
**适用场景**：多智能体协同、协议集成演示、技术验证

### 8.3 监控和日志

#### 8.3.1 日志配置
```typescript
// 日志配置
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: './logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: './logs/combined.log' 
    }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

#### 8.3.2 健康检查
```typescript
// 健康检查端点
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseHealth(),
      a2aHub: await checkA2AHubHealth(),
      mcpServer: await checkMCPServerHealth(),
      agents: await checkAgentsHealth()
    }
  };
  
  const isHealthy = Object.values(health.services)
    .every(service => service.status === 'ok');
  
  res.status(isHealthy ? 200 : 503).json(health);
});
```

## 9. 开发计划和里程碑

### 9.1 项目时间线

#### 9.1.1 总体时间规划
```mermaid
gantt
    title 多智能体协同工作演示项目开发计划
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析        :done, req, 2024-12-01, 1d
    概要设计        :active, arch, 2024-12-02, 2d
    详细设计        :detail, after arch, 3d
    
    section 开发阶段
    基础架构搭建     :infra, after detail, 2d
    协议层开发      :protocol, after infra, 3d
    智能体开发      :agents, after protocol, 3d
    前端开发        :frontend, after protocol, 4d
    集成测试        :integration, after agents, 2d
    
    section 部署阶段
    容器化部署      :deploy, after integration, 1d
    文档编写        :docs, after integration, 2d
    演示准备        :demo, after deploy, 1d
```

#### 9.1.2 详细里程碑

**第一阶段：基础架构（2天）**
- [ ] 项目结构搭建
- [ ] 开发环境配置
- [ ] 基础依赖安装
- [ ] Docker环境准备

**第二阶段：协议层开发（3天）**
- [ ] MCP服务器实现
- [ ] A2A协议中心开发
- [ ] AGUI服务器搭建
- [ ] 协议间通信测试

**第三阶段：智能体开发（3天）**
- [ ] 协调智能体实现
- [ ] 文档分析智能体开发
- [ ] 内容处理智能体开发
- [ ] 界面交互智能体开发

**第四阶段：前端开发（4天）**
- [ ] React应用搭建
- [ ] 文件上传组件
- [ ] 进度展示组件
- [ ] 结果展示组件
- [ ] AGUI协议集成

**第五阶段：集成测试（2天）**
- [ ] 端到端测试
- [ ] 协议交互测试
- [ ] 性能测试
- [ ] 错误处理测试

**第六阶段：部署和文档（3天）**
- [ ] 容器化部署
- [ ] 部署文档编写
- [ ] 使用说明文档
- [ ] 演示脚本准备

### 9.2 开发资源分配

#### 9.2.1 人员分工
- **架构师**：负责整体架构设计和技术选型
- **后端开发**：负责协议层和智能体开发
- **前端开发**：负责React应用和AGUI集成
- **测试工程师**：负责集成测试和质量保证

#### 9.2.2 技术风险评估

| 风险项 | 风险等级 | 影响程度 | 缓解措施 |
|--------|---------|---------|----------|
| 协议兼容性 | 中 | 高 | 提前技术验证，准备备选方案 |
| 智能体协作复杂度 | 高 | 中 | 简化协作流程，分步实现 |
| 前端实时更新 | 中 | 中 | 使用成熟的WebSocket库 |
| 大模型API稳定性 | 中 | 中 | 实现重试机制和降级策略 |
| 部署环境复杂度 | 低 | 低 | 使用Docker简化部署 |

### 9.3 质量保证计划

#### 9.3.1 代码质量标准
- **代码覆盖率**：≥80%
- **ESLint检查**：0错误，0警告
- **TypeScript严格模式**：启用所有严格检查
- **代码审查**：所有代码必须经过审查

#### 9.3.2 测试策略
```typescript
// 测试分层策略
interface TestStrategy {
  unit: {
    coverage: '≥90%';
    tools: ['Jest', 'Testing Library'];
    scope: ['智能体逻辑', '协议实现', '工具函数'];
  };
  
  integration: {
    coverage: '≥80%';
    tools: ['Supertest', 'WebSocket测试'];
    scope: ['API接口', '协议交互', '数据流'];
  };
  
  e2e: {
    coverage: '核心流程100%';
    tools: ['Playwright', 'Cypress'];
    scope: ['完整用户流程', '协议协作'];
  };
}
```

### 9.4 交付物清单

#### 9.4.1 代码交付物
- [ ] 完整的源代码仓库
- [ ] Docker容器镜像
- [ ] 部署配置文件
- [ ] 数据库迁移脚本

#### 9.4.2 文档交付物
- [ ] 概要设计说明书
- [ ] 详细设计说明书
- [ ] API接口文档
- [ ] 部署运维文档
- [ ] 用户使用手册
- [ ] 演示操作指南

#### 9.4.3 演示交付物
- [ ] 可运行的演示环境
- [ ] 演示数据和脚本
- [ ] 协议交互展示
- [ ] 技术特性说明

---

**文档版本**：v1.0  
**创建时间**：2024年12月  
**文档状态**：待审核  
**下一阶段**：详细设计