import express from 'express';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { a2aHub } from '../services/a2aHub';
import { logger } from '../utils/logger';

const router = express.Router();

// A2A协议消息处理接口
router.post('/message', asyncHandler(async (req, res) => {
  const { from, to, type, payload } = req.body;
  
  if (!from || !to || !type || !payload) {
    throw new CustomError('Missing required fields: from, to, type, payload', 400);
  }

  const validTypes = ['request', 'response', 'notification'];
  if (!validTypes.includes(type)) {
    throw new CustomError(`Invalid message type. Must be one of: ${validTypes.join(', ')}`, 400);
  }

  try {
    const messageId = await a2aHub.sendMessage({
      from,
      to,
      type,
      payload,
      timestamp: new Date()
    });

    res.json({
      success: true,
      data: {
        messageId,
        status: 'sent'
      },
      message: 'A2A message sent successfully'
    });
  } catch (error) {
    logger.error('A2A message sending failed:', error);
    throw new CustomError('Failed to send A2A message', 500);
  }
}));

// 智能体注册接口
router.post('/register', asyncHandler(async (req, res) => {
  const { agentId, name, type, capabilities, endpoint } = req.body;
  
  if (!agentId || !name || !type || !capabilities) {
    throw new CustomError('Missing required fields: agentId, name, type, capabilities', 400);
  }

  try {
    await a2aHub.registerAgent({
      id: agentId,
      name,
      type,
      capabilities,
      endpoint,
      status: 'active',
      registeredAt: new Date(),
      lastHeartbeat: new Date()
    });

    res.json({
      success: true,
      data: {
        agentId,
        status: 'registered'
      },
      message: 'Agent registered successfully'
    });
  } catch (error) {
    logger.error('Agent registration failed:', error);
    throw new CustomError('Failed to register agent', 500);
  }
}));

// 智能体注销接口
router.delete('/register/:agentId', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  
  const agent = await a2aHub.getAgent(agentId);
  if (!agent) {
    throw new CustomError('Agent not found', 404);
  }

  await a2aHub.unregisterAgent(agentId);
  
  res.json({
    success: true,
    message: 'Agent unregistered successfully'
  });
}));

// 智能体心跳接口
router.post('/heartbeat/:agentId', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  const { status, metrics } = req.body;
  
  const agent = await a2aHub.getAgent(agentId);
  if (!agent) {
    throw new CustomError('Agent not found', 404);
  }

  await a2aHub.updateHeartbeat(agentId, {
    status: status || 'active',
    metrics,
    timestamp: new Date()
  });
  
  res.json({
    success: true,
    data: {
      agentId,
      status: 'heartbeat_received'
    }
  });
}));

// 获取智能体发现列表
router.get('/discover', asyncHandler(async (req, res) => {
  const { capability, type } = req.query;
  
  const agents = await a2aHub.discoverAgents({
    capability: capability as string,
    type: type as string
  });
  
  res.json({
    success: true,
    data: {
      agents: agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        type: agent.type,
        capabilities: agent.capabilities,
        status: agent.status,
        endpoint: agent.endpoint
      })),
      count: agents.length
    }
  });
}));

// 任务协调接口
router.post('/coordinate', asyncHandler(async (req, res) => {
  const { taskId, coordinatorId, participants, workflow } = req.body;
  
  if (!taskId || !coordinatorId || !participants || !workflow) {
    throw new CustomError('Missing required fields: taskId, coordinatorId, participants, workflow', 400);
  }

  try {
    const coordination = await a2aHub.coordinateTask({
      taskId,
      coordinatorId,
      participants,
      workflow,
      startTime: new Date()
    });

    res.json({
      success: true,
      data: {
        coordinationId: coordination.id,
        status: coordination.status,
        participants: coordination.participants
      },
      message: 'Task coordination initiated successfully'
    });
  } catch (error) {
    logger.error('Task coordination failed:', error);
    throw new CustomError('Failed to coordinate task', 500);
  }
}));

// 获取协调状态
router.get('/coordinate/:coordinationId', asyncHandler(async (req, res) => {
  const { coordinationId } = req.params;
  
  const coordination = await a2aHub.getCoordination(coordinationId);
  if (!coordination) {
    throw new CustomError('Coordination not found', 404);
  }

  res.json({
    success: true,
    data: {
      id: coordination.id,
      taskId: coordination.taskId,
      coordinatorId: coordination.coordinatorId,
      status: coordination.status,
      participants: coordination.participants,
      workflow: coordination.workflow,
      progress: coordination.progress,
      startTime: coordination.startTime,
      endTime: coordination.endTime
    }
  });
}));

// 状态同步接口
router.post('/sync', asyncHandler(async (req, res) => {
  const { agentId, state, version } = req.body;
  
  if (!agentId || !state) {
    throw new CustomError('Missing required fields: agentId, state', 400);
  }

  try {
    const syncResult = await a2aHub.syncState({
      agentId,
      state,
      version: version || 1,
      timestamp: new Date()
    });

    res.json({
      success: true,
      data: {
        agentId,
        syncStatus: syncResult.status,
        version: syncResult.version,
        conflicts: syncResult.conflicts
      }
    });
  } catch (error) {
    logger.error('State synchronization failed:', error);
    throw new CustomError('Failed to synchronize state', 500);
  }
}));

// 获取消息历史
router.get('/messages', asyncHandler(async (req, res) => {
  const { agentId, limit = 50, offset = 0, type } = req.query;
  
  const messages = await a2aHub.getMessageHistory({
    agentId: agentId as string,
    limit: Number(limit),
    offset: Number(offset),
    type: type as string
  });
  
  res.json({
    success: true,
    data: {
      messages: messages.map(msg => ({
        id: msg.id,
        from: msg.from,
        to: msg.to,
        type: msg.type,
        payload: msg.payload,
        timestamp: msg.timestamp,
        status: msg.status
      })),
      count: messages.length
    }
  });
}));

export { router as a2aRoutes };