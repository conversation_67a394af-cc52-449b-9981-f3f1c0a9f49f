import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { a2aH<PERSON>, Agent, TaskDistribution } from './a2aHub';
import { documentService } from './documentService';

interface AgentConfig {
  qwenApiKey: string;
  qwenApiUrl: string;
  maxRetries: number;
  timeout: number;
}

interface QwenRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream?: boolean;
}

interface QwenResponse {
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class AgentService extends EventEmitter {
  private config: AgentConfig;
  private activeAgents: Map<string, any> = new Map();

  constructor() {
    super();
    
    this.config = {
      qwenApiKey: process.env.QWEN_API_KEY || '',
      qwenApiUrl: process.env.QWEN_API_URL || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
      maxRetries: 3,
      timeout: 30000
    };

    this.initializeAgents();
    this.setupEventHandlers();
  }

  // 初始化智能体
  private async initializeAgents(): Promise<void> {
    try {
      // 启动协调智能体
      await this.startCoordinatorAgent();
      
      // 启动文档分析智能体
      await this.startDocumentAnalyzerAgent();
      
      // 启动内容处理智能体
      await this.startContentProcessorAgent();
      
      // 启动界面交互智能体
      await this.startUIInterfaceAgent();
      
      logger.info('All agents initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize agents:', error);
      throw error;
    }
  }

  // 设置事件处理器
  private setupEventHandlers(): void {
    // 监听A2A Hub的任务分发
    a2aHub.on('task:distributed', (task: TaskDistribution) => {
      this.handleTaskDistribution(task);
    });

    // 监听智能体状态变化
    a2aHub.on('agent:status_updated', (agent: Agent) => {
      this.emit('agent:status_changed', agent);
    });
  }

  // 处理任务分发
  private async handleTaskDistribution(task: TaskDistribution): Promise<void> {
    if (!task.assignedAgent) {
      return;
    }

    const agent = this.activeAgents.get(task.assignedAgent);
    if (!agent) {
      logger.error(`Agent not found: ${task.assignedAgent}`);
      return;
    }

    try {
      // 根据任务类型调用相应的处理方法
      switch (task.type) {
        case 'document_analysis':
          await this.processDocumentAnalysis(task);
          break;
        case 'document_summary':
          await this.processDocumentSummary(task);
          break;
        case 'content_processing':
          await this.processContentProcessing(task);
          break;
        case 'ui_update':
          await this.processUIUpdate(task);
          break;
        default:
          logger.warn(`Unknown task type: ${task.type}`);
      }
    } catch (error) {
      logger.error(`Task processing failed: ${task.id}`, error);
      await a2aHub.handleTaskError(task.id, error.message, task.assignedAgent);
    }
  }

  // 启动协调智能体
  private async startCoordinatorAgent(): Promise<void> {
    const agentId = 'coordinator-001';
    
    const agent = {
      id: agentId,
      name: 'Coordinator Agent',
      type: 'coordinator',
      processTask: async (task: TaskDistribution) => {
        logger.info(`Coordinator processing task: ${task.id}`);
        
        // 协调智能体主要负责任务编排和流程控制
        const result = {
          taskId: task.id,
          status: 'coordinated',
          workflow: this.generateWorkflow(task),
          timestamp: new Date()
        };
        
        return result;
      }
    };
    
    this.activeAgents.set(agentId, agent);
    logger.info(`Coordinator agent started: ${agentId}`);
  }

  // 启动文档分析智能体
  private async startDocumentAnalyzerAgent(): Promise<void> {
    const agentId = 'doc-analyzer-001';
    
    const agent = {
      id: agentId,
      name: 'Document Analyzer Agent',
      type: 'document_analyzer',
      processTask: async (task: TaskDistribution) => {
        logger.info(`Document analyzer processing task: ${task.id}`);
        return await this.processDocumentAnalysis(task);
      }
    };
    
    this.activeAgents.set(agentId, agent);
    logger.info(`Document analyzer agent started: ${agentId}`);
  }

  // 启动内容处理智能体
  private async startContentProcessorAgent(): Promise<void> {
    const agentId = 'content-processor-001';
    
    const agent = {
      id: agentId,
      name: 'Content Processor Agent',
      type: 'content_processor',
      processTask: async (task: TaskDistribution) => {
        logger.info(`Content processor processing task: ${task.id}`);
        
        if (task.type === 'document_summary') {
          return await this.processDocumentSummary(task);
        } else {
          return await this.processContentProcessing(task);
        }
      }
    };
    
    this.activeAgents.set(agentId, agent);
    logger.info(`Content processor agent started: ${agentId}`);
  }

  // 启动界面交互智能体
  private async startUIInterfaceAgent(): Promise<void> {
    const agentId = 'ui-interface-001';
    
    const agent = {
      id: agentId,
      name: 'UI Interface Agent',
      type: 'ui_interface',
      processTask: async (task: TaskDistribution) => {
        logger.info(`UI interface processing task: ${task.id}`);
        return await this.processUIUpdate(task);
      }
    };
    
    this.activeAgents.set(agentId, agent);
    logger.info(`UI interface agent started: ${agentId}`);
  }

  // 处理文档分析任务
  private async processDocumentAnalysis(task: TaskDistribution): Promise<any> {
    if (!task.documentId) {
      throw new Error('Document ID is required for analysis');
    }

    // 读取文档内容
    const content = await documentService.readDocumentContent(task.documentId);
    
    // 使用Qwen进行文档分析
    const analysisPrompt = `请分析以下文档内容，提取关键信息：

文档内容：
${content}

请提供：
1. 文档主题和类型
2. 关键实体（人名、地名、组织等）
3. 重要概念和术语
4. 文档结构分析
5. 情感倾向分析

请以JSON格式返回结果。`;

    const qwenResponse = await this.callQwenAPI([
      {
        role: 'system',
        content: '你是一个专业的文档分析专家，擅长提取文档中的关键信息和进行深度分析。'
      },
      {
        role: 'user',
        content: analysisPrompt
      }
    ]);

    let analysisResult;
    try {
      analysisResult = JSON.parse(qwenResponse);
    } catch {
      // 如果解析失败，创建结构化结果
      analysisResult = {
        topic: '文档分析',
        type: '未知类型',
        entities: [],
        concepts: [],
        structure: qwenResponse,
        sentiment: 'neutral',
        rawAnalysis: qwenResponse
      };
    }

    // 更新文档处理结果
    await documentService.updateDocumentResults(task.documentId, {
      analysis: analysisResult
    });

    // 完成任务
    await a2aHub.handleTaskCompletion(task.id, analysisResult, task.assignedAgent!);
    
    return analysisResult;
  }

  // 处理文档摘要任务
  private async processDocumentSummary(task: TaskDistribution): Promise<any> {
    if (!task.documentId) {
      throw new Error('Document ID is required for summary');
    }

    // 读取文档内容
    const content = await documentService.readDocumentContent(task.documentId);
    
    const maxLength = task.parameters?.maxLength || 500;
    const includeKeywords = task.parameters?.includeKeywords || true;
    
    // 使用Qwen生成摘要
    const summaryPrompt = `请为以下文档生成摘要：

文档内容：
${content}

要求：
1. 摘要长度不超过${maxLength}字
2. 突出文档的核心内容和要点
3. 保持客观和准确
${includeKeywords ? '4. 提取5-10个关键词' : ''}

请以JSON格式返回，包含summary和keywords字段。`;

    const qwenResponse = await this.callQwenAPI([
      {
        role: 'system',
        content: '你是一个专业的文档摘要专家，擅长提取文档核心内容并生成简洁准确的摘要。'
      },
      {
        role: 'user',
        content: summaryPrompt
      }
    ]);

    let summaryResult;
    try {
      summaryResult = JSON.parse(qwenResponse);
    } catch {
      // 如果解析失败，创建基本结构
      summaryResult = {
        summary: qwenResponse.substring(0, maxLength),
        keywords: [],
        rawSummary: qwenResponse
      };
    }

    // 更新文档处理结果
    await documentService.updateDocumentResults(task.documentId, {
      summary: summaryResult.summary,
      keywords: summaryResult.keywords
    });

    // 完成任务
    await a2aHub.handleTaskCompletion(task.id, summaryResult, task.assignedAgent!);
    
    return summaryResult;
  }

  // 处理内容处理任务
  private async processContentProcessing(task: TaskDistribution): Promise<any> {
    const { type, parameters } = task;
    
    let result;
    switch (type) {
      case 'sentiment_analysis':
        result = await this.processSentimentAnalysis(task);
        break;
      case 'keyword_extraction':
        result = await this.processKeywordExtraction(task);
        break;
      case 'entity_recognition':
        result = await this.processEntityRecognition(task);
        break;
      default:
        throw new Error(`Unknown content processing type: ${type}`);
    }

    await a2aHub.handleTaskCompletion(task.id, result, task.assignedAgent!);
    return result;
  }

  // 处理UI更新任务
  private async processUIUpdate(task: TaskDistribution): Promise<any> {
    const { type, parameters } = task;
    
    const result = {
      taskId: task.id,
      updateType: type,
      parameters,
      timestamp: new Date(),
      status: 'ui_updated'
    };

    // 这里可以添加实际的UI更新逻辑
    logger.info(`UI update processed: ${type}`);
    
    await a2aHub.handleTaskCompletion(task.id, result, task.assignedAgent!);
    return result;
  }

  // 调用Qwen API
  private async callQwenAPI(messages: Array<{role: string, content: string}>): Promise<string> {
    if (!this.config.qwenApiKey) {
      throw new Error('Qwen API key is not configured');
    }

    const request: QwenRequest = {
      model: 'qwen-max',
      messages: messages as any,
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9
    };

    try {
      const response = await fetch(this.config.qwenApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.qwenApiKey}`,
          'X-DashScope-SSE': 'disable'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`Qwen API error: ${response.status} ${response.statusText}`);
      }

      const data: QwenResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response from Qwen API');
      }

      return data.choices[0].message.content;
    } catch (error) {
      logger.error('Qwen API call failed:', error);
      throw error;
    }
  }

  // 生成工作流
  private generateWorkflow(task: TaskDistribution): any {
    return {
      taskId: task.id,
      steps: [
        {
          step: 1,
          name: 'Task Analysis',
          status: 'completed',
          timestamp: new Date()
        },
        {
          step: 2,
          name: 'Resource Allocation',
          status: 'completed',
          timestamp: new Date()
        },
        {
          step: 3,
          name: 'Execution Planning',
          status: 'in_progress',
          timestamp: new Date()
        }
      ],
      estimatedCompletion: new Date(Date.now() + 60000)
    };
  }

  // 处理情感分析
  private async processSentimentAnalysis(task: TaskDistribution): Promise<any> {
    if (!task.documentId) {
      throw new Error('Document ID is required for sentiment analysis');
    }

    const content = await documentService.readDocumentContent(task.documentId);
    
    const prompt = `请分析以下文本的情感倾向：

${content}

请返回JSON格式，包含：
1. overall_sentiment: positive/negative/neutral
2. confidence: 0-1之间的置信度
3. emotions: 具体情感标签数组
4. explanation: 简要说明`;

    const response = await this.callQwenAPI([
      { role: 'system', content: '你是情感分析专家。' },
      { role: 'user', content: prompt }
    ]);

    try {
      return JSON.parse(response);
    } catch {
      return {
        overall_sentiment: 'neutral',
        confidence: 0.5,
        emotions: [],
        explanation: response
      };
    }
  }

  // 处理关键词提取
  private async processKeywordExtraction(task: TaskDistribution): Promise<any> {
    if (!task.documentId) {
      throw new Error('Document ID is required for keyword extraction');
    }

    const content = await documentService.readDocumentContent(task.documentId);
    const maxKeywords = task.parameters?.maxKeywords || 10;
    
    const prompt = `请从以下文本中提取${maxKeywords}个最重要的关键词：

${content}

请返回JSON格式，包含keywords数组，每个关键词包含word和importance字段。`;

    const response = await this.callQwenAPI([
      { role: 'system', content: '你是关键词提取专家。' },
      { role: 'user', content: prompt }
    ]);

    try {
      return JSON.parse(response);
    } catch {
      return {
        keywords: [],
        rawResponse: response
      };
    }
  }

  // 处理实体识别
  private async processEntityRecognition(task: TaskDistribution): Promise<any> {
    if (!task.documentId) {
      throw new Error('Document ID is required for entity recognition');
    }

    const content = await documentService.readDocumentContent(task.documentId);
    
    const prompt = `请识别以下文本中的命名实体：

${content}

请返回JSON格式，包含：
1. persons: 人名数组
2. organizations: 组织机构数组
3. locations: 地名数组
4. dates: 日期数组
5. others: 其他实体数组`;

    const response = await this.callQwenAPI([
      { role: 'system', content: '你是命名实体识别专家。' },
      { role: 'user', content: prompt }
    ]);

    try {
      return JSON.parse(response);
    } catch {
      return {
        persons: [],
        organizations: [],
        locations: [],
        dates: [],
        others: [],
        rawResponse: response
      };
    }
  }

  // 获取智能体状态
  getAgentStatus(agentId: string): any {
    return this.activeAgents.get(agentId);
  }

  // 获取所有活跃智能体
  getActiveAgents(): any[] {
    return Array.from(this.activeAgents.values());
  }

  // 关闭服务
  shutdown(): void {
    this.activeAgents.clear();
    logger.info('Agent service shutdown completed');
  }
}

export const agentService = new AgentService();
export { AgentConfig, QwenRequest, QwenResponse };