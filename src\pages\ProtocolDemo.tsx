import { useState, useEffect, useRef } from 'react';
import { 
  MessageSquare, 
  Send, 
  Bot, 
  User, 
  // Zap, 
  Activity, 
  Clock,
  CheckCircle,
  AlertCircle,
  Copy,
  Download,
  Trash2,
  // Settings,
  Play,
  Pause
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';
import { toast } from 'sonner';

interface DemoProtocolMessage {
  id: string;
  type: 'A2A' | 'AGUI' | 'MCP';
  direction: 'sent' | 'received';
  timestamp: string;
  sender: string;
  receiver: string;
  content: any;
  status: 'pending' | 'sent' | 'delivered' | 'failed';
}

interface MessageTemplate {
  id: string;
  name: string;
  type: 'A2A' | 'AGUI' | 'MCP';
  description: string;
  template: any;
}

export default function ProtocolDemo() {
  const { isConnected } = useAppStore();
  const { socket } = useSocket();
  const [selectedProtocol, setSelectedProtocol] = useState<'A2A' | 'AGUI' | 'MCP'>('A2A');
  const [messageInput, setMessageInput] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [demoMessages, setDemoMessages] = useState<DemoProtocolMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 消息模板
  const messageTemplates: MessageTemplate[] = [
    {
      id: 'a2a_register',
      name: 'A2A 智能体注册',
      type: 'A2A',
      description: '注册新的智能体到系统',
      template: {
        type: 'agent_register',
        agentId: 'demo_agent_001',
        name: '演示智能体',
        agentType: 'content_processor',
        capabilities: ['text_analysis', 'sentiment_analysis'],
        metadata: {
          version: '1.0.0',
          description: '用于演示的内容处理智能体'
        }
      }
    },
    {
      id: 'a2a_task_distribute',
      name: 'A2A 任务分发',
      type: 'A2A',
      description: '分发任务给智能体',
      template: {
        type: 'task_distribute',
        taskId: 'task_' + Date.now(),
        taskType: 'document_analysis',
        priority: 'medium',
        targetAgent: 'document_analyzer',
        payload: {
          documentId: 'doc_001',
          analysisType: 'full_analysis'
        }
      }
    },
    {
      id: 'a2a_heartbeat',
      name: 'A2A 心跳检测',
      type: 'A2A',
      description: '发送心跳信号',
      template: {
        type: 'heartbeat',
        agentId: 'coordinator',
        timestamp: new Date().toISOString(),
        status: 'active',
        metrics: {
          cpuUsage: 45.2,
          memoryUsage: 67.8,
          tasksInQueue: 3
        }
      }
    },
    {
      id: 'agui_file_upload',
      name: 'AGUI 文件上传进度',
      type: 'AGUI',
      description: '更新文件上传进度',
      template: {
        uploadId: 'upload_' + Date.now(),
        progress: 75,
        fileName: 'demo_document.pdf',
        fileSize: 2048576,
        status: 'uploading'
      }
    },
    {
      id: 'agui_task_update',
      name: 'AGUI 任务状态更新',
      type: 'AGUI',
      description: '更新任务执行状态',
      template: {
        taskId: 'task_001',
        status: 'running',
        progress: 60,
        message: '正在进行文档内容分析...',
        estimatedTime: 120
      }
    },
    {
      id: 'agui_agent_status',
      name: 'AGUI 智能体状态',
      type: 'AGUI',
      description: '智能体状态响应',
      template: {
        agents: [
          {
            id: 'coordinator',
            name: '协调智能体',
            status: 'active',
            currentTask: null
          },
          {
            id: 'document_analyzer',
            name: '文档分析智能体',
            status: 'busy',
            currentTask: 'task_001'
          }
        ]
      }
    },
    {
      id: 'mcp_list_tools',
      name: 'MCP 列出工具',
      type: 'MCP',
      description: '获取可用工具列表',
      template: {
        method: 'tools/list',
        params: {}
      }
    },
    {
      id: 'mcp_call_tool',
      name: 'MCP 调用工具',
      type: 'MCP',
      description: '调用文档上传工具',
      template: {
        method: 'tools/call',
        params: {
          name: 'upload_document',
          arguments: {
            file_path: '/path/to/document.pdf',
            document_type: 'pdf'
          }
        }
      }
    },
    {
      id: 'mcp_read_resource',
      name: 'MCP 读取资源',
      type: 'MCP',
      description: '读取文档列表资源',
      template: {
        method: 'resources/read',
        params: {
          uri: 'documents://list'
        }
      }
    }
  ];

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [demoMessages]);

  // 监听协议消息
  useEffect(() => {
    if (!socket || !isMonitoring) return;

    const handleA2AMessage = (data: any) => {
      setDemoMessages(prev => [...prev, {
          id: Math.random().toString(36).substr(2, 9),
          type: 'A2A',
          direction: 'received',
          timestamp: new Date().toISOString(),
          sender: data.sender || 'system',
          receiver: data.receiver || 'client',
          content: data,
          status: 'delivered'
        }]);
    };

    const handleAGUIMessage = (data: any) => {
      setDemoMessages(prev => [...prev, {
          id: Math.random().toString(36).substr(2, 9),
          type: 'AGUI',
          direction: 'received',
          timestamp: new Date().toISOString(),
          sender: 'server',
          receiver: 'client',
          content: data,
          status: 'delivered'
        }]);
    };

    // 监听各种事件
    socket.on('a2a_message', handleA2AMessage);
    socket.on('file_upload_progress', handleAGUIMessage);
    socket.on('document_status_update', handleAGUIMessage);
    socket.on('agent_status_response', handleAGUIMessage);
    socket.on('task_created', handleAGUIMessage);
    socket.on('task_updated', handleAGUIMessage);

    return () => {
      socket.off('a2a_message', handleA2AMessage);
      socket.off('file_upload_progress', handleAGUIMessage);
      socket.off('document_status_update', handleAGUIMessage);
      socket.off('agent_status_response', handleAGUIMessage);
      socket.off('task_created', handleAGUIMessage);
      socket.off('task_updated', handleAGUIMessage);
    };
  }, [socket, isMonitoring]);

  // 发送消息
  const sendMessage = () => {
    if (!messageInput.trim() || !isConnected) return;

    try {
      const content = JSON.parse(messageInput);
      
      const message: DemoProtocolMessage = {
        id: Math.random().toString(36).substr(2, 9),
        type: selectedProtocol,
        direction: 'sent',
        timestamp: new Date().toISOString(),
        sender: 'client',
        receiver: 'server',
        content,
        status: 'pending'
      };

      setDemoMessages(prev => [...prev, message]);

      // 根据协议类型发送消息
      switch (selectedProtocol) {
        case 'A2A':
          socket?.emit('a2a_message', content);
          break;
        case 'AGUI':
          // AGUI 消息根据类型发送
          if (content.type) {
            socket?.emit(content.type, content);
          } else {
            socket?.emit('agui_message', content);
          }
          break;
        case 'MCP':
          socket?.emit('mcp_request', content);
          break;
      }

      // 更新消息状态
      setTimeout(() => {
        setDemoMessages(prev => prev.map(m => m.id === message.id ? { ...m, status: 'sent' } : m));
      }, 100);

      setMessageInput('');
      toast.success('消息发送成功');
    } catch (error) {
      toast.error('消息格式错误，请检查 JSON 格式');
    }
  };

  // 使用模板
  const useTemplate = (template: MessageTemplate) => {
    setSelectedProtocol(template.type);
    setMessageInput(JSON.stringify(template.template, null, 2));
    setSelectedTemplate(template.id);
  };

  // 复制消息
  const copyMessage = (content: any) => {
    navigator.clipboard.writeText(JSON.stringify(content, null, 2));
    toast.success('消息已复制到剪贴板');
  };

  // 导出消息历史
  const exportMessages = () => {
    const data = JSON.stringify(demoMessages, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `protocol_messages_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('消息历史已导出');
  };

  // 清空消息历史
  const clearMessages = () => {
    setDemoMessages([]);
    toast.success('消息历史已清空');
  };

  // 获取协议颜色
  const getProtocolColor = (type: string) => {
    switch (type) {
      case 'A2A':
        return 'text-blue-600 bg-blue-100';
      case 'AGUI':
        return 'text-green-600 bg-green-100';
      case 'MCP':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-3 h-3 text-yellow-500" />;
      case 'sent':
        return <Send className="w-3 h-3 text-blue-500" />;
      case 'delivered':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-3 h-3 text-red-500" />;
      default:
        return <Activity className="w-3 h-3 text-gray-500" />;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const filteredMessages = demoMessages.filter(msg => 
    selectedProtocol === 'A2A' ? true : msg.type === selectedProtocol
  );

  const filteredTemplates = messageTemplates.filter(template => 
    template.type === selectedProtocol
  );

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">协议演示</h1>
          <p className="text-muted-foreground mt-1">
            实时演示 A2A、AGUI 和 MCP 协议的消息交互
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsMonitoring(!isMonitoring)}
            className={cn(
              "btn-secondary btn-sm",
              isMonitoring && "bg-green-100 text-green-700"
            )}
          >
            {isMonitoring ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
            {isMonitoring ? '停止监听' : '开始监听'}
          </button>
          
          <button
            onClick={exportMessages}
            className="btn-secondary btn-sm"
          >
            <Download className="w-4 h-4 mr-1" />
            导出历史
          </button>
          
          <button
            onClick={clearMessages}
            className="btn-secondary btn-sm text-red-600"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            清空历史
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 消息模板 */}
        <div className="space-y-4">
          <div className="bg-card rounded-lg border p-4">
            <h3 className="text-lg font-semibold text-foreground mb-4">消息模板</h3>
            
            <div className="space-y-2">
              {filteredTemplates.map((template) => (
                <button
                  key={template.id}
                  onClick={() => useTemplate(template)}
                  className={cn(
                    "w-full text-left p-3 rounded-lg border transition-colors",
                    selectedTemplate === template.id 
                      ? "border-primary bg-primary/5" 
                      : "border-border hover:bg-muted/50"
                  )}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={cn(
                      "protocol-badge text-xs px-2 py-1 rounded-full",
                      getProtocolColor(template.type)
                    )}>
                      {template.type}
                    </span>
                  </div>
                  <h4 className="font-medium text-foreground text-sm">
                    {template.name}
                  </h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    {template.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 消息交互区域 */}
        <div className="lg:col-span-3 space-y-4">
          {/* 协议选择 */}
          <div className="bg-card rounded-lg border p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">协议选择</h3>
              <div className={cn(
                "flex items-center space-x-2 px-3 py-1 rounded-lg text-sm",
                isConnected ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
              )}>
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  isConnected ? "bg-green-500" : "bg-red-500"
                )} />
                {isConnected ? '已连接' : '未连接'}
              </div>
            </div>
            
            <div className="flex space-x-2">
              {(['A2A', 'AGUI', 'MCP'] as const).map((protocol) => (
                <button
                  key={protocol}
                  onClick={() => setSelectedProtocol(protocol)}
                  className={cn(
                    "px-4 py-2 rounded-lg font-medium transition-colors",
                    selectedProtocol === protocol
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground hover:bg-muted/80"
                  )}
                >
                  {protocol}
                </button>
              ))}
            </div>
          </div>

          {/* 消息历史 */}
          <div className="bg-card rounded-lg border">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-foreground">消息历史</h3>
                <span className="text-sm text-muted-foreground">
                  {filteredMessages.length} 条消息
                </span>
              </div>
            </div>
            
            <div className="h-96 overflow-y-auto p-4 space-y-3">
              {filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex items-start space-x-3 p-3 rounded-lg",
                    message.direction === 'sent' 
                      ? "bg-blue-50 ml-8" 
                      : "bg-gray-50 mr-8"
                  )}
                >
                  <div className={cn(
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                    message.direction === 'sent' 
                      ? "bg-blue-500 text-white" 
                      : "bg-gray-500 text-white"
                  )}>
                    {message.direction === 'sent' ? 
                      <User className="w-4 h-4" /> : 
                      <Bot className="w-4 h-4" />
                    }
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={cn(
                        "protocol-badge text-xs px-2 py-1 rounded-full",
                        getProtocolColor(message.type)
                      )}>
                        {message.type}
                      </span>
                      
                      <span className="text-xs text-muted-foreground">
                        {message.sender} → {message.receiver}
                      </span>
                      
                      <span className="text-xs text-muted-foreground">
                        {formatTime(message.timestamp)}
                      </span>
                      
                      {getStatusIcon(message.status)}
                    </div>
                    
                    <div className="bg-white rounded border p-2 text-xs font-mono">
                      <pre className="whitespace-pre-wrap text-foreground">
                        {JSON.stringify(message.content, null, 2)}
                      </pre>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => copyMessage(message.content)}
                    className="flex-shrink-0 p-1 text-gray-500 hover:text-gray-700 transition-colors"
                    title="复制消息"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              ))}
              
              {filteredMessages.length === 0 && (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <h4 className="text-lg font-medium text-foreground mb-2">暂无消息</h4>
                  <p className="text-muted-foreground">
                    选择模板或手动输入消息开始演示
                  </p>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* 消息输入 */}
          <div className="bg-card rounded-lg border p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-foreground">发送消息</h3>
              <span className={cn(
                "protocol-badge text-xs px-2 py-1 rounded-full",
                getProtocolColor(selectedProtocol)
              )}>
                {selectedProtocol}
              </span>
            </div>
            
            <div className="space-y-3">
              <textarea
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                placeholder={`输入 ${selectedProtocol} 协议消息 (JSON 格式)...`}
                className="input-field w-full h-32 font-mono text-sm resize-none"
              />
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setMessageInput('')}
                    className="btn-secondary btn-sm"
                  >
                    清空
                  </button>
                  
                  <button
                    onClick={() => {
                      try {
                        const formatted = JSON.stringify(JSON.parse(messageInput), null, 2);
                        setMessageInput(formatted);
                        toast.success('JSON 格式化成功');
                      } catch (error) {
                        toast.error('JSON 格式错误');
                      }
                    }}
                    className="btn-secondary btn-sm"
                  >
                    格式化
                  </button>
                </div>
                
                <button
                  onClick={sendMessage}
                  disabled={!messageInput.trim() || !isConnected}
                  className="btn-primary"
                >
                  <Send className="w-4 h-4 mr-2" />
                  发送消息
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}