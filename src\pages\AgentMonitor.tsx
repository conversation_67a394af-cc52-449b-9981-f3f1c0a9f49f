import { useState, useEffect } from 'react';
import {
  Bot, 
  Activity, 
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';
import { toast } from 'sonner';

interface AgentMetrics {
  cpuUsage: number;
  memoryUsage: number;
  tasksCompleted: number;
  averageResponseTime: number;
  uptime: number;
}

interface AgentLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  agentId: string;
}

export default function AgentMonitor() {
  const { agents, updateAgent } = useAppStore();
  const { } = useSocket();
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [agentMetrics, setAgentMetrics] = useState<Record<string, AgentMetrics>>({});
  const [agentLogs, setAgentLogs] = useState<AgentLog[]>([]);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 获取智能体指标
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/api/agents/metrics');
        if (response.ok) {
          const metrics = await response.json();
          setAgentMetrics(metrics);
        }
      } catch (error) {
        console.error('Failed to fetch agent metrics:', error);
      }
    };

    fetchMetrics();
    
    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, 5000); // 每5秒更新
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // 生成模拟日志
  useEffect(() => {
    const generateLogs = () => {
      const logs: AgentLog[] = [
        {
          id: '1',
          timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
          level: 'info',
          message: '智能体启动完成，开始监听任务',
          agentId: 'coordinator'
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          level: 'success',
          message: '文档分析任务完成，耗时 2.3 秒',
          agentId: 'document_analyzer'
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
          level: 'warning',
          message: '任务队列积压，建议增加处理能力',
          agentId: 'content_processor'
        },
        {
          id: '4',
          timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
          level: 'info',
          message: 'UI 组件状态同步完成',
          agentId: 'ui_interface'
        },
        {
          id: '5',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          level: 'error',
          message: 'API 调用失败，正在重试',
          agentId: 'content_processor'
        }
      ];
      setAgentLogs(logs);
    };

    generateLogs();
  }, []);

  // 生成模拟指标
  useEffect(() => {
    const generateMetrics = () => {
      const metrics: Record<string, AgentMetrics> = {};
      agents.forEach(agent => {
        metrics[agent.id] = {
          cpuUsage: Math.random() * 100,
          memoryUsage: Math.random() * 100,
          tasksCompleted: Math.floor(Math.random() * 50),
          averageResponseTime: Math.random() * 2000 + 500,
          uptime: Math.random() * 86400 + 3600
        };
      });
      setAgentMetrics(metrics);
    };

    generateMetrics();
  }, [agents]);

  // 控制智能体
  const controlAgent = async (agentId: string, action: 'start' | 'stop' | 'restart') => {
    try {
      const response = await fetch(`/api/agents/${agentId}/${action}`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error(`操作失败: ${response.statusText}`);
      }

      const newStatus = action === 'start' ? 'active' : 
                       action === 'stop' ? 'inactive' : 'active';
      
      updateAgent(agentId, { status: newStatus });
      toast.success(`智能体 ${action} 操作成功`);
    } catch (error) {
      console.error('Control agent error:', error);
      toast.error(`智能体操作失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'busy':
        return 'text-yellow-600 bg-yellow-100';
      case 'inactive':
        return 'text-gray-600 bg-gray-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取日志级别颜色
  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  // 获取日志级别图标
  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'success':
        return <CheckCircle className="w-4 h-4" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  // 格式化时间
  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatResponseTime = (ms: number) => {
    return `${ms.toFixed(0)}ms`;
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return '刚刚';
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
    return `${Math.floor(diffInMinutes / 1440)}天前`;
  };

  const selectedAgentData = selectedAgent ? agents.find(a => a.id === selectedAgent) : null;
  const selectedAgentMetrics = selectedAgent ? agentMetrics[selectedAgent] : null;
  const selectedAgentLogs = agentLogs.filter(log => !selectedAgent || log.agentId === selectedAgent);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">智能体监控</h1>
          <p className="text-muted-foreground mt-1">
            实时监控智能体状态和性能指标
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={cn(
              "btn-secondary btn-sm",
              autoRefresh && "bg-green-100 text-green-700"
            )}
          >
            {autoRefresh ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
            {autoRefresh ? '暂停刷新' : '开始刷新'}
          </button>
        </div>
      </div>

      {/* 智能体概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {agents.map((agent) => {
          const metrics = agentMetrics[agent.id];
          return (
            <div 
              key={agent.id} 
              className={cn(
                "agent-card cursor-pointer transition-all",
                selectedAgent === agent.id && "ring-2 ring-primary"
              )}
              onClick={() => setSelectedAgent(selectedAgent === agent.id ? null : agent.id)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Bot className="w-5 h-5 text-primary" />
                  <span className="font-medium text-foreground truncate">
                    {agent.name}
                  </span>
                </div>
                <span className={cn(
                  "status-indicator text-xs px-2 py-1 rounded-full",
                  getStatusColor(agent.status)
                )}>
                  {agent.status}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">类型</span>
                  <span className="text-foreground">{agent.type.replace('_', ' ')}</span>
                </div>
                
                {metrics && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">CPU</span>
                      <span className="text-foreground">{metrics.cpuUsage.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">内存</span>
                      <span className="text-foreground">{metrics.memoryUsage.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">任务</span>
                      <span className="text-foreground">{metrics.tasksCompleted}</span>
                    </div>
                  </>
                )}
              </div>
              
              <div className="flex items-center justify-between mt-3 pt-3 border-t">
                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      controlAgent(agent.id, agent.status === 'active' ? 'stop' : 'start');
                    }}
                    className={cn(
                      "p-1 rounded transition-colors",
                      agent.status === 'active' 
                        ? "text-red-500 hover:bg-red-100" 
                        : "text-green-500 hover:bg-green-100"
                    )}
                    title={agent.status === 'active' ? '停止' : '启动'}
                  >
                    {agent.status === 'active' ? 
                      <Pause className="w-4 h-4" /> : 
                      <Play className="w-4 h-4" />
                    }
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      controlAgent(agent.id, 'restart');
                    }}
                    className="p-1 text-blue-500 hover:bg-blue-100 rounded transition-colors"
                    title="重启"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: 打开设置对话框
                    }}
                    className="p-1 text-gray-500 hover:bg-gray-100 rounded transition-colors"
                    title="设置"
                  >
                    <AlertCircle className="w-4 h-4" />
                  </button>
                </div>
                
                {metrics && (
                  <div className="text-xs text-muted-foreground">
                    运行 {formatUptime(metrics.uptime)}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 详细信息 */}
      {selectedAgentData && selectedAgentMetrics && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 性能指标 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 实时指标 */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                {selectedAgentData.name} - 实时指标
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-blue-500" />
                      <span className="text-sm text-muted-foreground">CPU 使用率</span>
                    </div>
                    <span className="text-sm font-medium text-foreground">
                      {selectedAgentMetrics.cpuUsage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill bg-blue-500" 
                      style={{ width: `${selectedAgentMetrics.cpuUsage}%` }}
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-muted-foreground">内存使用率</span>
                    </div>
                    <span className="text-sm font-medium text-foreground">
                      {selectedAgentMetrics.memoryUsage.toFixed(1)}%
                    </span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill bg-green-500" 
                      style={{ width: `${selectedAgentMetrics.memoryUsage}%` }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-foreground">
                    {selectedAgentMetrics.tasksCompleted}
                  </div>
                  <div className="text-sm text-muted-foreground">已完成任务</div>
                </div>
                
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-foreground">
                    {formatResponseTime(selectedAgentMetrics.averageResponseTime)}
                  </div>
                  <div className="text-sm text-muted-foreground">平均响应时间</div>
                </div>
                
                <div className="text-center p-3 rounded-lg bg-muted/50">
                  <div className="text-2xl font-bold text-foreground">
                    {formatUptime(selectedAgentMetrics.uptime)}
                  </div>
                  <div className="text-sm text-muted-foreground">运行时间</div>
                </div>
              </div>
            </div>

            {/* 活动日志 */}
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-foreground">活动日志</h3>
                <button
                  onClick={() => setSelectedAgent(null)}
                  className="btn-secondary btn-sm"
                >
                  显示所有日志
                </button>
              </div>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {selectedAgentLogs.map((log) => (
                  <div key={log.id} className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50">
                    <div className={cn("mt-0.5", getLogLevelColor(log.level))}>
                      {getLogLevelIcon(log.level)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className={cn(
                          "text-xs px-2 py-1 rounded-full font-medium",
                          getLogLevelColor(log.level),
                          "bg-current bg-opacity-10"
                        )}>
                          {log.level.toUpperCase()}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatTimeAgo(log.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm text-foreground mt-1">
                        {log.message}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 智能体信息 */}
          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">基本信息</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">名称</span>
                  <span className="text-sm font-medium text-foreground">
                    {selectedAgentData.name}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">类型</span>
                  <span className="text-sm font-medium text-foreground">
                    {selectedAgentData.type.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">状态</span>
                  <span className={cn(
                    "text-xs px-2 py-1 rounded-full font-medium",
                    getStatusColor(selectedAgentData.status)
                  )}>
                    {selectedAgentData.status}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">版本</span>
                  <span className="text-sm font-medium text-foreground">
                    v1.0.0
                  </span>
                </div>
              </div>
            </div>

            {/* 能力描述 */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">能力描述</h3>
              
              <div className="space-y-2 text-sm text-muted-foreground">
                {selectedAgentData.type === 'coordinator' && (
                  <p>负责任务协调和智能体间的通信，确保系统高效运行。</p>
                )}
                {selectedAgentData.type === 'document_analyzer' && (
                  <p>专门处理文档分析任务，包括内容提取、结构分析和元数据生成。</p>
                )}
                {selectedAgentData.type === 'content_processor' && (
                  <p>处理文本内容，包括情感分析、关键词提取和实体识别。</p>
                )}
                {selectedAgentData.type === 'ui_interface' && (
                  <p>管理用户界面交互，处理用户请求和界面状态同步。</p>
                )}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">快速操作</h3>
              
              <div className="space-y-2">
                <button
                  onClick={() => controlAgent(selectedAgentData.id, 
                    selectedAgentData.status === 'active' ? 'stop' : 'start'
                  )}
                  className={cn(
                    "w-full btn-sm",
                    selectedAgentData.status === 'active' ? "btn-secondary" : "btn-primary"
                  )}
                >
                  {selectedAgentData.status === 'active' ? (
                    <><Pause className="w-4 h-4 mr-2" />停止智能体</>
                  ) : (
                    <><Play className="w-4 h-4 mr-2" />启动智能体</>
                  )}
                </button>
                
                <button
                  onClick={() => controlAgent(selectedAgentData.id, 'restart')}
                  className="w-full btn-secondary btn-sm"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  重启智能体
                </button>
                
                <button className="w-full btn-secondary btn-sm">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  配置设置
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}