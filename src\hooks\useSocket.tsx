import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  emit: (event: string, data?: any) => void;
  on: (event: string, callback: (data: any) => void) => void;
  off: (event: string, callback?: (data: any) => void) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // 创建Socket连接
    const newSocket = io('http://localhost:8000', {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    // 连接事件处理
    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setConnected(true);
      toast.success('已连接到服务器');
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setConnected(false);
      toast.error('与服务器连接断开');
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnected(false);
      toast.error('连接服务器失败');
    });

    newSocket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts');
      setConnected(true);
      toast.success('已重新连接到服务器');
    });

    newSocket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
      toast.error('重连失败');
    });

    // AGUI协议事件处理
    newSocket.on('agui:file:upload:progress', (data) => {
      console.log('File upload progress:', data);
    });

    newSocket.on('agui:document:status:update', (data) => {
      console.log('Document status update:', data);
    });

    newSocket.on('agui:agents:status:response', (data) => {
      console.log('Agents status response:', data);
    });

    newSocket.on('agui:task:created', (data) => {
      console.log('Task created:', data);
      toast.success(`任务已创建: ${data.data.taskId}`);
    });

    newSocket.on('agui:task:update', (data) => {
      console.log('Task update:', data);
    });

    newSocket.on('agui:agent:update', (data) => {
      console.log('Agent update:', data);
    });

    newSocket.on('agui:progress:update', (data) => {
      console.log('Progress update:', data);
    });

    newSocket.on('agui:error', (data) => {
      console.error('AGUI error:', data);
      toast.error(`错误: ${data.data.error}`);
    });

    setSocket(newSocket);

    // 清理函数
    return () => {
      newSocket.close();
    };
  }, []);

  const emit = (event: string, data?: any) => {
    if (socket && connected) {
      socket.emit(event, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  };

  const on = (event: string, callback: (data: any) => void) => {
    if (socket) {
      socket.on(event, callback);
    }
  };

  const off = (event: string, callback?: (data: any) => void) => {
    if (socket) {
      if (callback) {
        socket.off(event, callback);
      } else {
        socket.off(event);
      }
    }
  };

  const value: SocketContextType = {
    socket,
    connected,
    emit,
    on,
    off,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
}

export function useSocket() {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}