import { useState, useEffect } from 'react';
import { 
  // Settings as SettingsIcon, 
  Save, 
  RotateCcw, 
  Bell, 
  Shield, 
  Palette, 
  // Globe, 
  // Database,
  // Zap,
  Monitor,
  Volume2,
  // Eye,
  // Lock,
  Server,
  AlertTriangle,
  // CheckCircle,
  // Info
} from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { cn } from '../utils/cn';
import { toast } from 'sonner';

interface SystemSettings {
  // 通知设置
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
    taskUpdates: boolean;
    agentStatus: boolean;
    systemAlerts: boolean;
  };
  
  // 显示设置
  display: {
    theme: 'light' | 'dark' | 'system';
    language: 'zh-CN' | 'en-US';
    timezone: string;
    dateFormat: string;
    compactMode: boolean;
    animations: boolean;
  };
  
  // 系统设置
  system: {
    autoRefresh: boolean;
    refreshInterval: number;
    maxLogEntries: number;
    enableDebugMode: boolean;
    performanceMode: boolean;
  };
  
  // 安全设置
  security: {
    sessionTimeout: number;
    requireAuth: boolean;
    enableAuditLog: boolean;
    allowRemoteAccess: boolean;
  };
  
  // 协议设置
  protocols: {
    a2a: {
      enabled: boolean;
      port: number;
      heartbeatInterval: number;
      maxRetries: number;
    };
    agui: {
      enabled: boolean;
      reconnectAttempts: number;
      bufferSize: number;
    };
    mcp: {
      enabled: boolean;
      timeout: number;
      maxConcurrentRequests: number;
    };
  };
}

export default function Settings() {
  const { setTheme } = useTheme();
  const [settings, setSettings] = useState<SystemSettings>({
    notifications: {
      enabled: true,
      sound: true,
      desktop: false,
      email: false,
      taskUpdates: true,
      agentStatus: true,
      systemAlerts: true
    },
    display: {
      theme: 'system',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      compactMode: false,
      animations: true
    },
    system: {
      autoRefresh: true,
      refreshInterval: 5000,
      maxLogEntries: 1000,
      enableDebugMode: false,
      performanceMode: false
    },
    security: {
      sessionTimeout: 3600,
      requireAuth: false,
      enableAuditLog: true,
      allowRemoteAccess: false
    },
    protocols: {
      a2a: {
        enabled: true,
        port: 8001,
        heartbeatInterval: 30000,
        maxRetries: 3
      },
      agui: {
        enabled: true,
        reconnectAttempts: 5,
        bufferSize: 1024
      },
      mcp: {
        enabled: true,
        timeout: 10000,
        maxConcurrentRequests: 10
      }
    }
  });
  
  const [activeTab, setActiveTab] = useState('display');
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };

    loadSettings();
  }, []);

  // 更新设置
  const updateSettings = (section: keyof SystemSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  // 更新嵌套设置
  const updateNestedSettings = (section: keyof SystemSettings, subsection: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...(prev[section] as any)[subsection],
          [key]: value
        }
      }
    }));
    setHasChanges(true);
  };

  // 保存设置
  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        throw new Error('保存失败');
      }

      setHasChanges(false);
      toast.success('设置保存成功');
    } catch (error) {
      console.error('Save settings error:', error);
      toast.error('设置保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 重置设置
  const resetSettings = () => {
    // 重置为默认值
    setSettings({
      notifications: {
        enabled: true,
        sound: true,
        desktop: false,
        email: false,
        taskUpdates: true,
        agentStatus: true,
        systemAlerts: true
      },
      display: {
        theme: 'system',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD HH:mm:ss',
        compactMode: false,
        animations: true
      },
      system: {
        autoRefresh: true,
        refreshInterval: 5000,
        maxLogEntries: 1000,
        enableDebugMode: false,
        performanceMode: false
      },
      security: {
        sessionTimeout: 3600,
        requireAuth: false,
        enableAuditLog: true,
        allowRemoteAccess: false
      },
      protocols: {
        a2a: {
          enabled: true,
          port: 8001,
          heartbeatInterval: 30000,
          maxRetries: 3
        },
        agui: {
          enabled: true,
          reconnectAttempts: 5,
          bufferSize: 1024
        },
        mcp: {
          enabled: true,
          timeout: 10000,
          maxConcurrentRequests: 10
        }
      }
    });
    setHasChanges(true);
    toast.info('设置已重置为默认值');
  };

  // 测试通知
  const testNotification = () => {
    if (settings.notifications.enabled) {
      toast.success('这是一个测试通知');
      
      if (settings.notifications.desktop && 'Notification' in window) {
        if (Notification.permission === 'granted') {
          new Notification('多智能体系统', {
            body: '这是一个桌面通知测试',
            icon: '/favicon.ico'
          });
        } else if (Notification.permission !== 'denied') {
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
              new Notification('多智能体系统', {
                body: '这是一个桌面通知测试',
                icon: '/favicon.ico'
              });
            }
          });
        }
      }
    } else {
      toast.error('通知功能已禁用');
    }
  };

  const tabs = [
    { id: 'display', name: '显示', icon: Palette },
    { id: 'notifications', name: '通知', icon: Bell },
    { id: 'system', name: '系统', icon: Monitor },
    { id: 'security', name: '安全', icon: Shield },
    { id: 'protocols', name: '协议', icon: Server }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">系统设置</h1>
          <p className="text-muted-foreground mt-1">
            配置系统参数和个人偏好
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <div className="flex items-center space-x-2 text-yellow-600">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">有未保存的更改</span>
            </div>
          )}
          
          <button
            onClick={resetSettings}
            className="btn-secondary"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            重置
          </button>
          
          <button
            onClick={saveSettings}
            disabled={!hasChanges || isSaving}
            className="btn-primary"
          >
            {isSaving ? (
              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            保存设置
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 设置导航 */}
        <div className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors",
                  activeTab === tab.id
                    ? "bg-primary text-primary-foreground"
                    : "bg-card text-foreground hover:bg-muted border"
                )}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{tab.name}</span>
              </button>
            );
          })}
        </div>

        {/* 设置内容 */}
        <div className="lg:col-span-3">
          <div className="bg-card rounded-lg border p-6">
            {/* 显示设置 */}
            {activeTab === 'display' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Palette className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">显示设置</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        主题模式
                      </label>
                      <select
                        value={settings.display.theme}
                        onChange={(e) => {
                          const newTheme = e.target.value as 'light' | 'dark' | 'system';
                          updateSettings('display', 'theme', newTheme);
                          setTheme(newTheme);
                        }}
                        className="input-field w-full"
                      >
                        <option value="light">浅色模式</option>
                        <option value="dark">深色模式</option>
                        <option value="system">跟随系统</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        语言
                      </label>
                      <select
                        value={settings.display.language}
                        onChange={(e) => updateSettings('display', 'language', e.target.value)}
                        className="input-field w-full"
                      >
                        <option value="zh-CN">简体中文</option>
                        <option value="en-US">English</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        时区
                      </label>
                      <select
                        value={settings.display.timezone}
                        onChange={(e) => updateSettings('display', 'timezone', e.target.value)}
                        className="input-field w-full"
                      >
                        <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                        <option value="America/New_York">America/New_York (UTC-5)</option>
                        <option value="Europe/London">Europe/London (UTC+0)</option>
                        <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        日期格式
                      </label>
                      <select
                        value={settings.display.dateFormat}
                        onChange={(e) => updateSettings('display', 'dateFormat', e.target.value)}
                        className="input-field w-full"
                      >
                        <option value="YYYY-MM-DD HH:mm:ss">2024-01-01 12:00:00</option>
                        <option value="MM/DD/YYYY HH:mm">01/01/2024 12:00</option>
                        <option value="DD/MM/YYYY HH:mm">01/01/2024 12:00</option>
                      </select>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-foreground">
                          紧凑模式
                        </label>
                        <input
                          type="checkbox"
                          checked={settings.display.compactMode}
                          onChange={(e) => updateSettings('display', 'compactMode', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-foreground">
                          启用动画
                        </label>
                        <input
                          type="checkbox"
                          checked={settings.display.animations}
                          onChange={(e) => updateSettings('display', 'animations', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 通知设置 */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Bell className="w-5 h-5 text-primary" />
                    <h3 className="text-lg font-semibold text-foreground">通知设置</h3>
                  </div>
                  <button
                    onClick={testNotification}
                    className="btn-secondary btn-sm"
                  >
                    <Volume2 className="w-4 h-4 mr-1" />
                    测试通知
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                    <div>
                      <h4 className="font-medium text-foreground">启用通知</h4>
                      <p className="text-sm text-muted-foreground">接收系统通知和提醒</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.notifications.enabled}
                      onChange={(e) => updateSettings('notifications', 'enabled', e.target.checked)}
                      className="rounded border-gray-300"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">声音提醒</h4>
                        <p className="text-sm text-muted-foreground">播放通知声音</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.sound}
                        onChange={(e) => updateSettings('notifications', 'sound', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">桌面通知</h4>
                        <p className="text-sm text-muted-foreground">显示桌面通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.desktop}
                        onChange={(e) => updateSettings('notifications', 'desktop', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">邮件通知</h4>
                        <p className="text-sm text-muted-foreground">发送邮件提醒</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.email}
                        onChange={(e) => updateSettings('notifications', 'email', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">任务更新</h4>
                        <p className="text-sm text-muted-foreground">任务状态变化通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.taskUpdates}
                        onChange={(e) => updateSettings('notifications', 'taskUpdates', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">智能体状态</h4>
                        <p className="text-sm text-muted-foreground">智能体状态变化通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.agentStatus}
                        onChange={(e) => updateSettings('notifications', 'agentStatus', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">系统警告</h4>
                        <p className="text-sm text-muted-foreground">系统错误和警告通知</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.notifications.systemAlerts}
                        onChange={(e) => updateSettings('notifications', 'systemAlerts', e.target.checked)}
                        disabled={!settings.notifications.enabled}
                        className="rounded border-gray-300"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 系统设置 */}
            {activeTab === 'system' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Monitor className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">系统设置</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">自动刷新</h4>
                        <p className="text-sm text-muted-foreground">自动更新数据</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.system.autoRefresh}
                        onChange={(e) => updateSettings('system', 'autoRefresh', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        刷新间隔 (毫秒)
                      </label>
                      <input
                        type="number"
                        value={settings.system.refreshInterval}
                        onChange={(e) => updateSettings('system', 'refreshInterval', parseInt(e.target.value))}
                        min="1000"
                        max="60000"
                        step="1000"
                        disabled={!settings.system.autoRefresh}
                        className="input-field w-full"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        最大日志条目
                      </label>
                      <input
                        type="number"
                        value={settings.system.maxLogEntries}
                        onChange={(e) => updateSettings('system', 'maxLogEntries', parseInt(e.target.value))}
                        min="100"
                        max="10000"
                        step="100"
                        className="input-field w-full"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">调试模式</h4>
                        <p className="text-sm text-muted-foreground">显示详细调试信息</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.system.enableDebugMode}
                        onChange={(e) => updateSettings('system', 'enableDebugMode', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
                      <div>
                        <h4 className="font-medium text-foreground">性能模式</h4>
                        <p className="text-sm text-muted-foreground">优化性能，减少动画</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.system.performanceMode}
                        onChange={(e) => updateSettings('system', 'performanceMode', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 安全设置 */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Shield className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">安全设置</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="p-4 rounded-lg bg-yellow-50 border border-yellow-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="w-5 h-5 text-yellow-600" />
                      <h4 className="font-medium text-yellow-800">安全提醒</h4>
                    </div>
                    <p className="text-sm text-yellow-700">
                      修改安全设置可能影响系统安全性，请谨慎操作。
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        会话超时 (秒)
                      </label>
                      <input
                        type="number"
                        value={settings.security.sessionTimeout}
                        onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                        min="300"
                        max="86400"
                        step="300"
                        className="input-field w-full"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <div>
                          <h4 className="font-medium text-foreground">需要身份验证</h4>
                          <p className="text-xs text-muted-foreground">访问系统需要登录</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={settings.security.requireAuth}
                          onChange={(e) => updateSettings('security', 'requireAuth', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                      </div>
                      
                      <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <div>
                          <h4 className="font-medium text-foreground">启用审计日志</h4>
                          <p className="text-xs text-muted-foreground">记录用户操作</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={settings.security.enableAuditLog}
                          onChange={(e) => updateSettings('security', 'enableAuditLog', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                      </div>
                      
                      <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <div>
                          <h4 className="font-medium text-foreground">允许远程访问</h4>
                          <p className="text-xs text-muted-foreground">允许外部网络访问</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={settings.security.allowRemoteAccess}
                          onChange={(e) => updateSettings('security', 'allowRemoteAccess', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 协议设置 */}
            {activeTab === 'protocols' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Server className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">协议设置</h3>
                </div>
                
                <div className="space-y-6">
                  {/* A2A 协议 */}
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium text-foreground">A2A 协议</h4>
                        <p className="text-sm text-muted-foreground">智能体间通信协议</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.protocols.a2a.enabled}
                        onChange={(e) => updateNestedSettings('protocols', 'a2a', 'enabled', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          端口
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.a2a.port}
                          onChange={(e) => updateNestedSettings('protocols', 'a2a', 'port', parseInt(e.target.value))}
                          disabled={!settings.protocols.a2a.enabled}
                          className="input-field w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          心跳间隔 (ms)
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.a2a.heartbeatInterval}
                          onChange={(e) => updateNestedSettings('protocols', 'a2a', 'heartbeatInterval', parseInt(e.target.value))}
                          disabled={!settings.protocols.a2a.enabled}
                          className="input-field w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          最大重试次数
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.a2a.maxRetries}
                          onChange={(e) => updateNestedSettings('protocols', 'a2a', 'maxRetries', parseInt(e.target.value))}
                          disabled={!settings.protocols.a2a.enabled}
                          className="input-field w-full"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* AGUI 协议 */}
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium text-foreground">AGUI 协议</h4>
                        <p className="text-sm text-muted-foreground">智能体图形界面协议</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.protocols.agui.enabled}
                        onChange={(e) => updateNestedSettings('protocols', 'agui', 'enabled', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          重连尝试次数
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.agui.reconnectAttempts}
                          onChange={(e) => updateNestedSettings('protocols', 'agui', 'reconnectAttempts', parseInt(e.target.value))}
                          disabled={!settings.protocols.agui.enabled}
                          className="input-field w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          缓冲区大小 (KB)
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.agui.bufferSize}
                          onChange={(e) => updateNestedSettings('protocols', 'agui', 'bufferSize', parseInt(e.target.value))}
                          disabled={!settings.protocols.agui.enabled}
                          className="input-field w-full"
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* MCP 协议 */}
                  <div className="p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium text-foreground">MCP 协议</h4>
                        <p className="text-sm text-muted-foreground">模型上下文协议</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.protocols.mcp.enabled}
                        onChange={(e) => updateNestedSettings('protocols', 'mcp', 'enabled', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          超时时间 (ms)
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.mcp.timeout}
                          onChange={(e) => updateNestedSettings('protocols', 'mcp', 'timeout', parseInt(e.target.value))}
                          disabled={!settings.protocols.mcp.enabled}
                          className="input-field w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-1">
                          最大并发请求
                        </label>
                        <input
                          type="number"
                          value={settings.protocols.mcp.maxConcurrentRequests}
                          onChange={(e) => updateNestedSettings('protocols', 'mcp', 'maxConcurrentRequests', parseInt(e.target.value))}
                          disabled={!settings.protocols.mcp.enabled}
                          className="input-field w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}