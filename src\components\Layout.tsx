import { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Upload, 
  Bot, 
  ListTodo, 
  Network, 
  Settings, 
  Menu,
  X,
  Sun,
  Moon,
  Monitor
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import { useTheme } from '../hooks/useTheme';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';

interface LayoutProps {
  children: ReactNode;
}

const navigation = [
  { name: '仪表板', href: '/', icon: Home },
  { name: '文档上传', href: '/upload', icon: Upload },
  { name: '智能体监控', href: '/agents', icon: Bot },
  { name: '任务管理', href: '/tasks', icon: ListTodo },
  { name: '协议演示', href: '/protocols', icon: Network },
  { name: '设置', href: '/settings', icon: Settings },
];

export default function Layout({ children }: LayoutProps) {
  const location = useLocation();
  const { sidebarCollapsed, setSidebarCollapsed } = useAppStore();
  const { theme, setTheme } = useTheme();
  const { connected } = useSocket();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return Sun;
      case 'dark':
        return Moon;
      default:
        return Monitor;
    }
  };

  const cycleTheme = () => {
    const themes = ['light', 'dark', 'system'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const ThemeIcon = getThemeIcon();

  return (
    <div className="min-h-screen bg-background">
      {/* 侧边栏 */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 flex flex-col bg-card border-r border-border transition-all duration-300",
        sidebarCollapsed ? "w-16" : "w-64"
      )}>
        {/* 头部 */}
        <div className="flex h-16 items-center justify-between px-4 border-b border-border">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="font-semibold text-foreground">多智能体协同</span>
            </div>
          )}
          <button
            onClick={toggleSidebar}
            className="p-2 rounded-md hover:bg-accent transition-colors"
          >
            {sidebarCollapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
          </button>
        </div>

        {/* 连接状态 */}
        <div className={cn(
          "px-4 py-2 border-b border-border",
          sidebarCollapsed && "px-2"
        )}>
          <div className={cn(
            "flex items-center space-x-2",
            sidebarCollapsed && "justify-center"
          )}>
            <div className={cn(
              "w-2 h-2 rounded-full",
              connected ? "bg-green-500" : "bg-red-500"
            )} />
            {!sidebarCollapsed && (
              <span className="text-sm text-muted-foreground">
                {connected ? '已连接' : '未连接'}
              </span>
            )}
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-2 py-4 space-y-1">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href;
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                  sidebarCollapsed && "justify-center px-2"
                )}
                title={sidebarCollapsed ? item.name : undefined}
              >
                <item.icon className={cn(
                  "w-5 h-5",
                  !sidebarCollapsed && "mr-3"
                )} />
                {!sidebarCollapsed && item.name}
              </Link>
            );
          })}
        </nav>

        {/* 底部工具 */}
        <div className="p-2 border-t border-border">
          <button
            onClick={cycleTheme}
            className={cn(
              "flex items-center w-full px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-colors",
              sidebarCollapsed && "justify-center px-2"
            )}
            title={sidebarCollapsed ? `主题: ${theme}` : undefined}
          >
            <ThemeIcon className={cn(
              "w-5 h-5",
              !sidebarCollapsed && "mr-3"
            )} />
            {!sidebarCollapsed && (
              <span className="capitalize">{theme === 'system' ? '跟随系统' : theme === 'light' ? '浅色' : '深色'}</span>
            )}
          </button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className={cn(
        "transition-all duration-300",
        sidebarCollapsed ? "ml-16" : "ml-64"
      )}>
        {/* 顶部栏 */}
        <header className="h-16 bg-card border-b border-border flex items-center justify-between px-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-foreground">
              {navigation.find(item => item.href === location.pathname)?.name || '多智能体协同工作演示系统'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* 实时状态指示器 */}
            <div className="flex items-center space-x-2">
              <div className={cn(
                "w-2 h-2 rounded-full animate-pulse",
                connected ? "bg-green-500" : "bg-red-500"
              )} />
              <span className="text-sm text-muted-foreground">
                {connected ? '实时同步' : '连接中断'}
              </span>
            </div>
            
            {/* 系统时间 */}
            <div className="text-sm text-muted-foreground">
              {new Date().toLocaleTimeString('zh-CN')}
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="p-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}