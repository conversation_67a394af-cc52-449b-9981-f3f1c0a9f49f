// import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Toaster } from 'sonner';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import DocumentUpload from './pages/DocumentUpload';
import AgentMonitor from './pages/AgentMonitor';
import TaskManager from './pages/TaskManager';
import ProtocolDemo from './pages/ProtocolDemo';
import Settings from './pages/Settings';
import { SocketProvider } from './hooks/useSocket';
import { useTheme } from './hooks/useTheme';

function App() {
  const { theme } = useTheme();

  return (
    <div className={theme === 'dark' ? 'dark' : ''}>
      <SocketProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/upload" element={<DocumentUpload />} />
            <Route path="/agents" element={<AgentMonitor />} />
            <Route path="/tasks" element={<TaskManager />} />
            <Route path="/protocols" element={<ProtocolDemo />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
        <Toaster 
          position="top-right" 
          theme={theme}
          richColors
          closeButton
        />
      </SocketProvider>
    </div>
  );
}

export default App;