import { Socket, Server } from 'socket.io';
import { logger } from '../utils/logger';
import { documentService } from '../services/documentService';
import { a2aHub } from '../services/a2aHub';

interface AGUIEvent {
  type: 'event' | 'state' | 'command';
  component: string;
  action: string;
  data: any;
  timestamp?: Date;
}

interface AGUIState {
  component: string;
  state: any;
  version: number;
  timestamp: Date;
}

export const aguiHandler = (socket: Socket, io: Server) => {
  logger.info(`AGUI client connected: ${socket.id}`);

  // 文件上传进度事件
  socket.on('agui:file:upload:start', async (data) => {
    try {
      logger.info('File upload started:', data);
      
      // 广播上传开始事件
      io.emit('agui:file:upload:progress', {
        type: 'event',
        component: 'FileUploader',
        action: 'upload_started',
        data: {
          filename: data.filename,
          size: data.size,
          progress: 0
        },
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('File upload start error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'FileUploader',
        action: 'error',
        data: { error: 'Failed to start file upload' }
      });
    }
  });

  // 文档处理状态更新
  socket.on('agui:document:process:status', async (data) => {
    try {
      const { documentId } = data;
      
      if (!documentId) {
        throw new Error('Document ID is required');
      }

      const document = await documentService.getDocumentById(documentId);
      const tasks = await documentService.getDocumentTasks(documentId);
      
      socket.emit('agui:document:status:update', {
        type: 'state',
        component: 'DocumentProcessor',
        action: 'status_update',
        data: {
          documentId,
          status: document?.status || 'unknown',
          progress: document?.progress || 0,
          tasks: tasks.map(task => ({
            id: task.id,
            type: task.type,
            status: task.status,
            progress: task.progress,
            agentId: task.agentId
          }))
        },
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Document status update error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'DocumentProcessor',
        action: 'error',
        data: { error: 'Failed to get document status' }
      });
    }
  });

  // 智能体状态查询
  socket.on('agui:agents:status:request', async () => {
    try {
      const agents = await a2aHub.getRegisteredAgents();
      
      socket.emit('agui:agents:status:response', {
        type: 'state',
        component: 'AgentMonitor',
        action: 'status_update',
        data: {
          agents: agents.map(agent => ({
            id: agent.id,
            name: agent.name,
            type: agent.type,
            status: agent.status,
            capabilities: agent.capabilities,
            lastHeartbeat: agent.lastHeartbeat
          })),
          totalCount: agents.length,
          activeCount: agents.filter(agent => agent.status === 'active').length
        },
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Agent status request error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'AgentMonitor',
        action: 'error',
        data: { error: 'Failed to get agent status' }
      });
    }
  });

  // 任务创建请求
  socket.on('agui:task:create', async (data) => {
    try {
      const { taskType, documentId, parameters } = data;
      
      if (!taskType || !documentId) {
        throw new Error('Task type and document ID are required');
      }

      const task = await a2aHub.distributeTask({
        type: taskType,
        documentId,
        parameters: parameters || {},
        priority: 'normal'
      });

      socket.emit('agui:task:created', {
        type: 'event',
        component: 'TaskManager',
        action: 'task_created',
        data: {
          taskId: task.id,
          assignedAgent: task.assignedAgent,
          status: task.status,
          estimatedTime: task.estimatedTime
        },
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Task creation error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'TaskManager',
        action: 'error',
        data: { error: 'Failed to create task' }
      });
    }
  });

  // 实时进度更新订阅
  socket.on('agui:subscribe:progress', (data) => {
    const { documentId } = data;
    
    if (documentId) {
      socket.join(`document:${documentId}`);
      logger.info(`Client ${socket.id} subscribed to document ${documentId} progress`);
    }
  });

  // 取消进度更新订阅
  socket.on('agui:unsubscribe:progress', (data) => {
    const { documentId } = data;
    
    if (documentId) {
      socket.leave(`document:${documentId}`);
      logger.info(`Client ${socket.id} unsubscribed from document ${documentId} progress`);
    }
  });

  // 组件状态同步
  socket.on('agui:state:sync', async (data: AGUIState) => {
    try {
      const { component, state, version } = data;
      
      // 广播状态更新到其他客户端
      socket.broadcast.emit('agui:state:update', {
        type: 'state',
        component,
        action: 'state_sync',
        data: {
          state,
          version: version + 1
        },
        timestamp: new Date()
      });
      
      logger.debug(`State synchronized for component: ${component}`);
    } catch (error) {
      logger.error('State synchronization error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'StateManager',
        action: 'error',
        data: { error: 'Failed to synchronize state' }
      });
    }
  });

  // 用户交互事件
  socket.on('agui:user:interaction', async (data) => {
    try {
      const { component, action, payload } = data;
      
      logger.info(`User interaction: ${component}.${action}`, payload);
      
      // 根据不同的交互类型处理
      switch (action) {
        case 'button_click':
          handleButtonClick(socket, component, payload);
          break;
        case 'form_submit':
          handleFormSubmit(socket, component, payload);
          break;
        case 'file_select':
          handleFileSelect(socket, component, payload);
          break;
        default:
          logger.warn(`Unknown interaction action: ${action}`);
      }
    } catch (error) {
      logger.error('User interaction error:', error);
      socket.emit('agui:error', {
        type: 'event',
        component: 'UserInterface',
        action: 'error',
        data: { error: 'Failed to process user interaction' }
      });
    }
  });

  // 错误处理
  socket.on('agui:error', (error) => {
    logger.error('AGUI client error:', error);
  });

  // 断开连接处理
  socket.on('disconnect', () => {
    logger.info(`AGUI client disconnected: ${socket.id}`);
  });
};

// 按钮点击处理
const handleButtonClick = (socket: Socket, component: string, payload: any) => {
  const { buttonId, data } = payload;
  
  socket.emit('agui:button:clicked', {
    type: 'event',
    component,
    action: 'button_clicked',
    data: {
      buttonId,
      result: 'success',
      ...data
    },
    timestamp: new Date()
  });
};

// 表单提交处理
const handleFormSubmit = (socket: Socket, component: string, payload: any) => {
  const { formId, formData } = payload;
  
  socket.emit('agui:form:submitted', {
    type: 'event',
    component,
    action: 'form_submitted',
    data: {
      formId,
      formData,
      result: 'success'
    },
    timestamp: new Date()
  });
};

// 文件选择处理
const handleFileSelect = (socket: Socket, component: string, payload: any) => {
  const { files } = payload;
  
  socket.emit('agui:file:selected', {
    type: 'event',
    component,
    action: 'file_selected',
    data: {
      files: files.map((file: any) => ({
        name: file.name,
        size: file.size,
        type: file.type
      })),
      count: files.length
    },
    timestamp: new Date()
  });
};

// 广播进度更新到订阅的客户端
export const broadcastProgress = (io: Server, documentId: string, progress: any) => {
  io.to(`document:${documentId}`).emit('agui:progress:update', {
    type: 'event',
    component: 'ProgressTracker',
    action: 'progress_update',
    data: {
      documentId,
      ...progress
    },
    timestamp: new Date()
  });
};

// 广播任务状态更新
export const broadcastTaskUpdate = (io: Server, taskUpdate: any) => {
  io.emit('agui:task:update', {
    type: 'event',
    component: 'TaskMonitor',
    action: 'task_update',
    data: taskUpdate,
    timestamp: new Date()
  });
};

// 广播智能体状态更新
export const broadcastAgentUpdate = (io: Server, agentUpdate: any) => {
  io.emit('agui:agent:update', {
    type: 'event',
    component: 'AgentMonitor',
    action: 'agent_update',
    data: agentUpdate,
    timestamp: new Date()
  });
};