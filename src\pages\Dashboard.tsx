import React, { useEffect, useState } from 'react';
import { 
  FileText, 
  Bot, 
  ListTodo, 
  Activity, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle,
  Zap
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';

interface StatCard {
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon: React.ComponentType<any>;
  color: string;
}

interface RecentActivity {
  id: string;
  type: 'document' | 'task' | 'agent';
  message: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

export default function Dashboard() {
  const { 
    documents, 
    agents, 
    tasks, 
    systemStats, 
    isConnected,
    setSystemStats 
  } = useAppStore();
  const { } = useSocket();
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);

  // 获取系统统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/system/stats');
        if (response.ok) {
          const stats = await response.json();
          setSystemStats(stats);
        }
      } catch (error) {
        console.error('Failed to fetch system stats:', error);
      }
    };

    fetchStats();
    const interval = setInterval(fetchStats, 30000); // 每30秒更新一次

    return () => clearInterval(interval);
  }, [setSystemStats]);

  // 生成模拟的最近活动
  useEffect(() => {
    const activities: RecentActivity[] = [
      {
        id: '1',
        type: 'document',
        message: '文档 "项目报告.pdf" 上传成功',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        status: 'success'
      },
      {
        id: '2',
        type: 'task',
        message: '文档分析任务已完成',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        status: 'success'
      },
      {
        id: '3',
        type: 'agent',
        message: '内容处理智能体状态更新',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        status: 'info'
      },
      {
        id: '4',
        type: 'task',
        message: '摘要生成任务正在进行',
        timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
        status: 'warning'
      },
      {
        id: '5',
        type: 'document',
        message: '文档处理完成，结果已生成',
        timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
        status: 'success'
      }
    ];
    setRecentActivities(activities);
  }, []);

  // 计算统计卡片数据
  const statCards: StatCard[] = [
    {
      title: '文档总数',
      value: documents.length,
      change: '+12%',
      trend: 'up',
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      title: '活跃智能体',
      value: agents.filter(agent => agent.status === 'active').length,
      change: '100%',
      trend: 'neutral',
      icon: Bot,
      color: 'bg-green-500'
    },
    {
      title: '进行中任务',
      value: tasks.filter(task => task.status === 'running').length,
      change: '-5%',
      trend: 'down',
      icon: ListTodo,
      color: 'bg-yellow-500'
    },
    {
      title: '系统运行时间',
      value: systemStats ? `${Math.floor(systemStats.uptime / 3600)}h` : '0h',
      change: '持续运行',
      trend: 'up',
      icon: Activity,
      color: 'bg-purple-500'
    }
  ];

  const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />;
      default:
        return <TrendingUp className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-4 h-4" />;
      case 'task':
        return <ListTodo className="w-4 h-4" />;
      case 'agent':
        return <Bot className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getActivityStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-blue-500" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return '刚刚';
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
    return `${Math.floor(diffInMinutes / 1440)}天前`;
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">仪表板</h1>
          <p className="text-muted-foreground mt-1">
            多智能体协同工作系统实时监控
          </p>
        </div>
        
        {/* 连接状态指示器 */}
        <div className={cn(
          "flex items-center space-x-2 px-3 py-2 rounded-lg",
          isConnected ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
        )}>
          <div className={cn(
            "w-2 h-2 rounded-full",
            isConnected ? "bg-green-500 animate-pulse" : "bg-red-500"
          )} />
          <span className="text-sm font-medium">
            {isConnected ? '实时连接' : '连接断开'}
          </span>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="bg-card rounded-lg border p-6 card-hover">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </p>
                <p className="text-2xl font-bold text-foreground mt-2">
                  {card.value}
                </p>
                {card.change && (
                  <div className="flex items-center mt-2 space-x-1">
                    {getTrendIcon(card.trend)}
                    <span className={cn(
                      "text-sm",
                      card.trend === 'up' ? "text-green-600" :
                      card.trend === 'down' ? "text-red-600" : "text-gray-600"
                    )}>
                      {card.change}
                    </span>
                  </div>
                )}
              </div>
              <div className={cn(
                "p-3 rounded-lg",
                card.color
              )}>
                <card.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 系统概览 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 智能体状态 */}
          <div className="bg-card rounded-lg border p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">智能体状态</h3>
              <Zap className="w-5 h-5 text-primary" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {agents.slice(0, 4).map((agent) => (
                <div key={agent.id} className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                  <div className={cn(
                    "w-3 h-3 rounded-full",
                    agent.status === 'active' ? "bg-green-500" :
                    agent.status === 'busy' ? "bg-yellow-500" :
                    agent.status === 'inactive' ? "bg-gray-500" : "bg-red-500"
                  )} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {agent.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {agent.type.replace('_', ' ')}
                    </p>
                  </div>
                  <span className={cn(
                    "status-indicator",
                    agent.status === 'active' ? "status-active" :
                    agent.status === 'busy' ? "status-busy" :
                    agent.status === 'inactive' ? "status-inactive" : "status-error"
                  )}>
                    {agent.status}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 任务进度 */}
          <div className="bg-card rounded-lg border p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">任务进度</h3>
              <ListTodo className="w-5 h-5 text-primary" />
            </div>
            
            <div className="space-y-3">
              {tasks.slice(0, 3).map((task) => (
                <div key={task.id} className="task-item">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-foreground">
                        {task.type.replace('_', ' ')}
                      </span>
                      <span className={cn(
                        "status-indicator",
                        task.status === 'completed' ? "status-active" :
                        task.status === 'running' ? "status-busy" :
                        task.status === 'pending' ? "status-inactive" : "status-error"
                      )}>
                        {task.status}
                      </span>
                    </div>
                    {task.progress !== undefined && (
                      <div className="mt-2">
                        <div className="progress-bar">
                          <div 
                            className="progress-fill" 
                            style={{ width: `${task.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground mt-1">
                          {task.progress}% 完成
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatTimeAgo(task.createdAt)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 最近活动 */}
        <div className="bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">最近活动</h3>
            <Activity className="w-5 h-5 text-primary" />
          </div>
          
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  {getActivityStatusIcon(activity.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-foreground">
                    {activity.message}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    {getActivityIcon(activity.type)}
                    <span className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}