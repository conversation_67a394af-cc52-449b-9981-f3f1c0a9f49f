import express from 'express';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { documentService } from '../services/documentService';
import { coordinatorAgent } from '../agents/coordinatorAgent';
import { logger } from '../utils/logger';

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = [
      'text/plain',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/markdown'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new CustomError('Invalid file type. Only TXT, PDF, DOC, DOCX, MD files are allowed.', 400));
    }
  }
});

// 文档上传接口
router.post('/upload', upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw new CustomError('No file uploaded', 400);
  }

  const documentId = uuidv4();
  const documentData = {
    id: documentId,
    filename: req.file.originalname,
    filepath: req.file.path,
    mimetype: req.file.mimetype,
    size: req.file.size,
    uploadTime: new Date()
  };

  try {
    // 保存文档信息
    await documentService.createDocument(documentData);
    
    // 异步启动文档处理流程
    coordinatorAgent.processDocument(documentId).catch(error => {
      logger.error('Document processing failed:', error);
    });

    res.json({
      success: true,
      data: {
        documentId,
        filename: req.file.originalname,
        size: req.file.size,
        status: 'uploaded'
      },
      message: 'File uploaded successfully'
    });
  } catch (error) {
    logger.error('Document upload failed:', error);
    throw new CustomError('Failed to process uploaded file', 500);
  }
}));

// 获取文档状态
router.get('/:documentId/status', asyncHandler(async (req, res) => {
  const { documentId } = req.params;
  
  const document = await documentService.getDocumentById(documentId);
  if (!document) {
    throw new CustomError('Document not found', 404);
  }

  const tasks = await documentService.getDocumentTasks(documentId);
  
  res.json({
    success: true,
    data: {
      documentId,
      filename: document.filename,
      status: document.status,
      progress: document.progress || 0,
      tasks: tasks.map(task => ({
        id: task.id,
        type: task.type,
        status: task.status,
        progress: task.progress,
        agentId: task.agentId
      })),
      createdAt: document.createdAt,
      updatedAt: document.updatedAt
    }
  });
}));

// 获取处理结果
router.get('/:documentId/result', asyncHandler(async (req, res) => {
  const { documentId } = req.params;
  
  const document = await documentService.getDocumentById(documentId);
  if (!document) {
    throw new CustomError('Document not found', 404);
  }

  if (document.status !== 'completed') {
    throw new CustomError('Document processing not completed', 400);
  }

  const result = await documentService.getProcessingResult(documentId);
  
  res.json({
    success: true,
    data: {
      documentId,
      filename: document.filename,
      result,
      completedAt: document.updatedAt
    }
  });
}));

// 获取文档列表
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  
  const documents = await documentService.getDocuments({
    page: Number(page),
    limit: Number(limit),
    status: status as string
  });
  
  res.json({
    success: true,
    data: documents
  });
}));

// 删除文档
router.delete('/:documentId', asyncHandler(async (req, res) => {
  const { documentId } = req.params;
  
  const document = await documentService.getDocumentById(documentId);
  if (!document) {
    throw new CustomError('Document not found', 404);
  }

  await documentService.deleteDocument(documentId);
  
  res.json({
    success: true,
    message: 'Document deleted successfully'
  });
}));

export { router as documentRoutes };