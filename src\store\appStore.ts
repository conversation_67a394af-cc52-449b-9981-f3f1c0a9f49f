import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 文档接口
export interface Document {
  id: string;
  name: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  uploadTime: string;
  uploadedAt: Date;
  status: 'uploaded' | 'processing' | 'completed' | 'error' | 'uploading';
  progress: number;
  type: string;
  content?: string;
  description?: string;
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    language?: string;
    encoding?: string;
  };
  processingResults?: {
    analysis?: any;
    summary?: string;
    keywords?: string[];
    entities?: any[];
    sentiment?: any;
  };
  error?: string;
}

// 智能体接口
interface Agent {
  id: string;
  name: string;
  type: 'coordinator' | 'document_analyzer' | 'content_processor' | 'ui_interface';
  status: 'active' | 'busy' | 'inactive' | 'error';
  capabilities: string[];
  lastHeartbeat: string;
  metadata?: {
    version?: string;
    description?: string;
    maxConcurrentTasks?: number;
    averageProcessingTime?: number;
  };
  performance?: {
    tasksCompleted: number;
    averageResponseTime: number;
    successRate: number;
    lastTaskTime?: string;
  };
}

// 任务类型定义
export type TaskStatus = 'pending' | 'assigned' | 'running' | 'completed' | 'error' | 'failed';
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

// 任务接口
export interface Task {
  id: string;
  title: string;
  description?: string;
  type: string;
  documentId?: string;
  status: TaskStatus;
  assignedAgent?: string;
  priority: TaskPriority;
  createdAt: string;
  updatedAt: Date;
  assignedAt?: string;
  completedAt?: string;
  estimatedTime?: number;
  estimatedDuration?: number;
  actualDuration?: number;
  progress: number;
  result?: any;
  error?: string;
}

// 协议消息接口
interface ProtocolMessage {
  id: string;
  protocol: 'MCP' | 'A2A' | 'AGUI';
  type: string;
  from: string;
  to: string;
  payload: any;
  timestamp: string;
  status?: 'sent' | 'received' | 'processed' | 'error';
}

// 系统统计接口
interface SystemStats {
  documents: {
    total: number;
    uploaded: number;
    processing: number;
    completed: number;
    error: number;
  };
  tasks: {
    total: number;
    pending: number;
    running: number;
    completed: number;
    error: number;
  };
  agents: {
    total: number;
    active: number;
    busy: number;
    inactive: number;
  };
  uptime: number;
  timestamp: string;
}

// 应用状态接口
interface AppState {
  // 文档管理
  documents: Document[];
  selectedDocument: Document | null;
  
  // 智能体管理
  agents: Agent[];
  
  // 任务管理
  tasks: Task[];
  
  // 协议消息
  protocolMessages: ProtocolMessage[];
  
  // 系统统计
  systemStats: SystemStats | null;
  
  // UI状态
  sidebarCollapsed: boolean;
  activeView: string;
  
  // 实时状态
  isConnected: boolean;
  lastUpdate: string;
  
  // Actions
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  removeDocument: (id: string) => void;
  setSelectedDocument: (document: Document | null) => void;
  
  setAgents: (agents: Agent[]) => void;
  updateAgent: (id: string, updates: Partial<Agent>) => void;
  
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (id: string, updates: Partial<Task>) => void;
  removeTask: (id: string) => void;
  
  addProtocolMessage: (message: ProtocolMessage) => void;
  clearProtocolMessages: () => void;
  
  setSystemStats: (stats: SystemStats) => void;
  
  setSidebarCollapsed: (collapsed: boolean) => void;
  setActiveView: (view: string) => void;
  setIsConnected: (connected: boolean) => void;
  
  // 辅助方法
  getDocumentById: (id: string) => Document | undefined;
  getAgentById: (id: string) => Agent | undefined;
  getTaskById: (id: string) => Task | undefined;
  getTasksByDocument: (documentId: string) => Task[];
  getTasksByAgent: (agentId: string) => Task[];
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      documents: [],
      selectedDocument: null,
      agents: [],
      tasks: [],
      protocolMessages: [],
      systemStats: null,
      sidebarCollapsed: false,
      activeView: 'dashboard',
      isConnected: false,
      lastUpdate: new Date().toISOString(),
      
      // 文档管理Actions
      setDocuments: (documents) => {
        set({ documents, lastUpdate: new Date().toISOString() });
      },
      
      addDocument: (document) => {
        set((state) => ({
          documents: [...state.documents, document],
          lastUpdate: new Date().toISOString()
        }));
      },
      
      updateDocument: (id, updates) => {
        set((state) => ({
          documents: state.documents.map(doc => 
            doc.id === id ? { ...doc, ...updates } : doc
          ),
          selectedDocument: state.selectedDocument?.id === id 
            ? { ...state.selectedDocument, ...updates } 
            : state.selectedDocument,
          lastUpdate: new Date().toISOString()
        }));
      },
      
      removeDocument: (id) => {
        set((state) => ({
          documents: state.documents.filter(doc => doc.id !== id),
          selectedDocument: state.selectedDocument?.id === id ? null : state.selectedDocument,
          tasks: state.tasks.filter(task => task.documentId !== id),
          lastUpdate: new Date().toISOString()
        }));
      },
      
      setSelectedDocument: (document) => {
        set({ selectedDocument: document });
      },
      
      // 智能体管理Actions
      setAgents: (agents) => {
        set({ agents, lastUpdate: new Date().toISOString() });
      },
      
      updateAgent: (id, updates) => {
        set((state) => ({
          agents: state.agents.map(agent => 
            agent.id === id ? { ...agent, ...updates } : agent
          ),
          lastUpdate: new Date().toISOString()
        }));
      },
      
      // 任务管理Actions
      setTasks: (tasks) => {
        set({ tasks, lastUpdate: new Date().toISOString() });
      },
      
      addTask: (task) => {
        set((state) => ({
          tasks: [...state.tasks, task],
          lastUpdate: new Date().toISOString()
        }));
      },
      
      updateTask: (id, updates) => {
        set((state) => ({
          tasks: state.tasks.map(task => 
            task.id === id ? { ...task, ...updates } : task
          ),
          lastUpdate: new Date().toISOString()
        }));
      },
      
      removeTask: (id) => {
        set((state) => ({
          tasks: state.tasks.filter(task => task.id !== id),
          lastUpdate: new Date().toISOString()
        }));
      },
      
      // 协议消息Actions
      addProtocolMessage: (message) => {
        set((state) => {
          const messages = [...state.protocolMessages, message];
          // 保持最近1000条消息
          if (messages.length > 1000) {
            messages.splice(0, messages.length - 1000);
          }
          return {
            protocolMessages: messages,
            lastUpdate: new Date().toISOString()
          };
        });
      },
      
      clearProtocolMessages: () => {
        set({ protocolMessages: [] });
      },
      
      // 系统统计Actions
      setSystemStats: (stats) => {
        set({ systemStats: stats, lastUpdate: new Date().toISOString() });
      },
      
      // UI状态Actions
      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed });
      },
      
      setActiveView: (view) => {
        set({ activeView: view });
      },
      
      setIsConnected: (connected) => {
        set({ isConnected: connected, lastUpdate: new Date().toISOString() });
      },
      
      // 辅助方法
      getDocumentById: (id) => {
        return get().documents.find(doc => doc.id === id);
      },
      
      getAgentById: (id) => {
        return get().agents.find(agent => agent.id === id);
      },
      
      getTaskById: (id) => {
        return get().tasks.find(task => task.id === id);
      },
      
      getTasksByDocument: (documentId) => {
        return get().tasks.filter(task => task.documentId === documentId);
      },
      
      getTasksByAgent: (agentId) => {
        return get().tasks.filter(task => task.assignedAgent === agentId);
      },
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        activeView: state.activeView,
      }),
    }
  )
);

// 导出类型
export type { Agent, ProtocolMessage, SystemStats };