import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { a2aHub } from './a2aHub';

interface Document {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  uploadTime: Date;
  status: 'uploaded' | 'processing' | 'completed' | 'error';
  progress: number;
  filePath: string;
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    language?: string;
    encoding?: string;
  };
  processingResults?: {
    analysis?: any;
    summary?: string;
    keywords?: string[];
    entities?: any[];
    sentiment?: any;
  };
  error?: string;
}

interface Task {
  id: string;
  documentId: string;
  type: 'analysis' | 'summary' | 'extraction' | 'translation';
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  agentId?: string;
  parameters: any;
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  estimatedTime?: number;
}

class DocumentService {
  private documents: Map<string, Document> = new Map();
  private tasks: Map<string, Task> = new Map();
  private uploadsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
    this.ensureUploadsDir();
  }

  private async ensureUploadsDir() {
    try {
      await fs.access(this.uploadsDir);
    } catch {
      await fs.mkdir(this.uploadsDir, { recursive: true });
      logger.info(`Created uploads directory: ${this.uploadsDir}`);
    }
  }

  // 保存上传的文档
  async saveDocument(file: Express.Multer.File): Promise<Document> {
    const documentId = uuidv4();
    const filename = `${documentId}_${file.originalname}`;
    const filePath = path.join(this.uploadsDir, filename);

    try {
      // 保存文件
      await fs.writeFile(filePath, file.buffer);

      const document: Document = {
        id: documentId,
        filename,
        originalName: file.originalname,
        size: file.size,
        mimeType: file.mimetype,
        uploadTime: new Date(),
        status: 'uploaded',
        progress: 0,
        filePath
      };

      this.documents.set(documentId, document);
      logger.info(`Document saved: ${documentId} - ${file.originalname}`);

      return document;
    } catch (error) {
      logger.error('Failed to save document:', error);
      throw new Error('Failed to save document');
    }
  }

  // 获取文档信息
  async getDocumentById(documentId: string): Promise<Document | undefined> {
    return this.documents.get(documentId);
  }

  // 获取所有文档
  async getAllDocuments(): Promise<Document[]> {
    return Array.from(this.documents.values());
  }

  // 更新文档状态
  async updateDocumentStatus(
    documentId: string, 
    status: Document['status'], 
    progress?: number,
    error?: string
  ): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    document.status = status;
    if (progress !== undefined) {
      document.progress = progress;
    }
    if (error) {
      document.error = error;
    }

    this.documents.set(documentId, document);
    logger.info(`Document status updated: ${documentId} - ${status}`);
  }

  // 更新文档处理结果
  async updateDocumentResults(documentId: string, results: any): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    document.processingResults = {
      ...document.processingResults,
      ...results
    };

    this.documents.set(documentId, document);
    logger.info(`Document results updated: ${documentId}`);
  }

  // 删除文档
  async deleteDocument(documentId: string): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    try {
      // 删除文件
      await fs.unlink(document.filePath);
      
      // 删除相关任务
      const documentTasks = Array.from(this.tasks.values())
        .filter(task => task.documentId === documentId);
      
      for (const task of documentTasks) {
        this.tasks.delete(task.id);
      }

      // 删除文档记录
      this.documents.delete(documentId);
      
      logger.info(`Document deleted: ${documentId}`);
    } catch (error) {
      logger.error('Failed to delete document:', error);
      throw new Error('Failed to delete document');
    }
  }

  // 读取文档内容
  async readDocumentContent(documentId: string): Promise<string> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    try {
      const content = await fs.readFile(document.filePath, 'utf-8');
      return content;
    } catch (error) {
      logger.error('Failed to read document content:', error);
      throw new Error('Failed to read document content');
    }
  }

  // 创建处理任务
  async createTask(
    documentId: string,
    type: Task['type'],
    parameters: any = {}
  ): Promise<Task> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    const taskId = uuidv4();
    const task: Task = {
      id: taskId,
      documentId,
      type,
      status: 'pending',
      progress: 0,
      parameters,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.tasks.set(taskId, task);
    logger.info(`Task created: ${taskId} - ${type} for document ${documentId}`);

    return task;
  }

  // 获取文档的所有任务
  async getDocumentTasks(documentId: string): Promise<Task[]> {
    return Array.from(this.tasks.values())
      .filter(task => task.documentId === documentId);
  }

  // 获取任务信息
  async getTaskById(taskId: string): Promise<Task | undefined> {
    return this.tasks.get(taskId);
  }

  // 更新任务状态
  async updateTaskStatus(
    taskId: string,
    status: Task['status'],
    progress?: number,
    result?: any,
    error?: string
  ): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    task.status = status;
    task.updatedAt = new Date();
    
    if (progress !== undefined) {
      task.progress = progress;
    }
    if (result !== undefined) {
      task.result = result;
    }
    if (error) {
      task.error = error;
    }

    this.tasks.set(taskId, task);
    logger.info(`Task status updated: ${taskId} - ${status}`);
  }

  // 分配任务给智能体
  async assignTaskToAgent(taskId: string, agentId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    task.agentId = agentId;
    task.status = 'running';
    task.updatedAt = new Date();

    this.tasks.set(taskId, task);
    logger.info(`Task assigned: ${taskId} to agent ${agentId}`);
  }

  // 开始文档处理流程
  async startDocumentProcessing(documentId: string): Promise<void> {
    const document = this.documents.get(documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    try {
      // 更新文档状态为处理中
      await this.updateDocumentStatus(documentId, 'processing', 10);

      // 创建分析任务
      const analysisTask = await this.createTask(documentId, 'analysis', {
        includeMetadata: true,
        extractEntities: true,
        analyzeSentiment: true
      });

      // 创建摘要任务
      const summaryTask = await this.createTask(documentId, 'summary', {
        maxLength: 500,
        includeKeywords: true
      });

      // 通过A2A Hub分发任务
      await a2aHub.distributeTask({
        id: analysisTask.id,
        type: 'document_analysis',
        documentId,
        parameters: analysisTask.parameters,
        priority: 'high'
      });

      await a2aHub.distributeTask({
        id: summaryTask.id,
        type: 'document_summary',
        documentId,
        parameters: summaryTask.parameters,
        priority: 'normal'
      });

      logger.info(`Document processing started: ${documentId}`);
    } catch (error) {
      logger.error('Failed to start document processing:', error);
      await this.updateDocumentStatus(documentId, 'error', 0, error.message);
      throw error;
    }
  }

  // 获取处理统计信息
  async getProcessingStats(): Promise<any> {
    const documents = Array.from(this.documents.values());
    const tasks = Array.from(this.tasks.values());

    return {
      documents: {
        total: documents.length,
        uploaded: documents.filter(d => d.status === 'uploaded').length,
        processing: documents.filter(d => d.status === 'processing').length,
        completed: documents.filter(d => d.status === 'completed').length,
        error: documents.filter(d => d.status === 'error').length
      },
      tasks: {
        total: tasks.length,
        pending: tasks.filter(t => t.status === 'pending').length,
        running: tasks.filter(t => t.status === 'running').length,
        completed: tasks.filter(t => t.status === 'completed').length,
        error: tasks.filter(t => t.status === 'error').length
      }
    };
  }

  // 清理过期文档和任务
  async cleanup(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    const now = new Date();
    const cutoff = new Date(now.getTime() - maxAge);

    // 清理过期文档
    for (const [id, document] of this.documents.entries()) {
      if (document.uploadTime < cutoff && document.status === 'completed') {
        try {
          await this.deleteDocument(id);
          logger.info(`Cleaned up expired document: ${id}`);
        } catch (error) {
          logger.error(`Failed to cleanup document ${id}:`, error);
        }
      }
    }

    // 清理过期任务
    for (const [id, task] of this.tasks.entries()) {
      if (task.createdAt < cutoff && task.status === 'completed') {
        this.tasks.delete(id);
        logger.info(`Cleaned up expired task: ${id}`);
      }
    }
  }
}

export const documentService = new DocumentService();
export { Document, Task };