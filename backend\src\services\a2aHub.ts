import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

interface Agent {
  id: string;
  name: string;
  type: 'coordinator' | 'document_analyzer' | 'content_processor' | 'ui_interface';
  status: 'active' | 'busy' | 'inactive' | 'error';
  capabilities: string[];
  endpoint?: string;
  lastHeartbeat: Date;
  metadata?: {
    version?: string;
    description?: string;
    maxConcurrentTasks?: number;
    averageProcessingTime?: number;
  };
  performance?: {
    tasksCompleted: number;
    averageResponseTime: number;
    successRate: number;
    lastTaskTime?: Date;
  };
}

interface A2AMessage {
  id: string;
  type: 'task' | 'response' | 'heartbeat' | 'discovery' | 'coordination' | 'status';
  from: string;
  to: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

interface TaskDistribution {
  id: string;
  type: string;
  documentId?: string;
  parameters: any;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assignedAgent?: string;
  status: 'pending' | 'assigned' | 'running' | 'completed' | 'error';
  createdAt: Date;
  assignedAt?: Date;
  completedAt?: Date;
  result?: any;
  error?: string;
  estimatedTime?: number;
}

class A2AHub extends EventEmitter {
  private agents: Map<string, Agent> = new Map();
  private messages: A2AMessage[] = [];
  private tasks: Map<string, TaskDistribution> = new Map();
  private heartbeatInterval: NodeJS.Timeout;
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    super();
    this.startHeartbeatMonitoring();
    this.startCleanupProcess();
    this.initializeBuiltInAgents();
  }

  // 初始化内置智能体
  private initializeBuiltInAgents() {
    const builtInAgents: Omit<Agent, 'lastHeartbeat'>[] = [
      {
        id: 'coordinator-001',
        name: 'Coordinator Agent',
        type: 'coordinator',
        status: 'active',
        capabilities: ['task_coordination', 'workflow_management', 'resource_allocation'],
        metadata: {
          version: '1.0.0',
          description: 'Main coordination agent for multi-agent workflows',
          maxConcurrentTasks: 10
        },
        performance: {
          tasksCompleted: 0,
          averageResponseTime: 0,
          successRate: 1.0
        }
      },
      {
        id: 'doc-analyzer-001',
        name: 'Document Analyzer Agent',
        type: 'document_analyzer',
        status: 'active',
        capabilities: ['document_analysis', 'text_extraction', 'metadata_extraction', 'entity_recognition'],
        metadata: {
          version: '1.0.0',
          description: 'Specialized agent for document analysis and processing',
          maxConcurrentTasks: 5
        },
        performance: {
          tasksCompleted: 0,
          averageResponseTime: 0,
          successRate: 1.0
        }
      },
      {
        id: 'content-processor-001',
        name: 'Content Processor Agent',
        type: 'content_processor',
        status: 'active',
        capabilities: ['content_summarization', 'keyword_extraction', 'sentiment_analysis', 'translation'],
        metadata: {
          version: '1.0.0',
          description: 'Agent for content processing and transformation',
          maxConcurrentTasks: 3
        },
        performance: {
          tasksCompleted: 0,
          averageResponseTime: 0,
          successRate: 1.0
        }
      },
      {
        id: 'ui-interface-001',
        name: 'UI Interface Agent',
        type: 'ui_interface',
        status: 'active',
        capabilities: ['ui_updates', 'user_interaction', 'progress_reporting', 'notification'],
        metadata: {
          version: '1.0.0',
          description: 'Agent for managing user interface interactions',
          maxConcurrentTasks: 8
        },
        performance: {
          tasksCompleted: 0,
          averageResponseTime: 0,
          successRate: 1.0
        }
      }
    ];

    builtInAgents.forEach(agent => {
      this.registerAgent({
        ...agent,
        lastHeartbeat: new Date()
      });
    });

    logger.info('Built-in agents initialized');
  }

  // 注册智能体
  registerAgent(agent: Agent): void {
    this.agents.set(agent.id, {
      ...agent,
      lastHeartbeat: new Date()
    });
    
    logger.info(`Agent registered: ${agent.id} - ${agent.name}`);
    this.emit('agent:registered', agent);
  }

  // 注销智能体
  unregisterAgent(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      this.agents.delete(agentId);
      logger.info(`Agent unregistered: ${agentId}`);
      this.emit('agent:unregistered', agent);
    }
  }

  // 更新智能体状态
  updateAgentStatus(agentId: string, status: Agent['status']): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = status;
      agent.lastHeartbeat = new Date();
      this.agents.set(agentId, agent);
      
      logger.debug(`Agent status updated: ${agentId} - ${status}`);
      this.emit('agent:status_updated', agent);
    }
  }

  // 处理心跳
  handleHeartbeat(agentId: string, metadata?: any): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.lastHeartbeat = new Date();
      if (metadata) {
        agent.metadata = { ...agent.metadata, ...metadata };
      }
      this.agents.set(agentId, agent);
      
      logger.debug(`Heartbeat received from agent: ${agentId}`);
    }
  }

  // 获取已注册的智能体
  getRegisteredAgents(): Agent[] {
    return Array.from(this.agents.values());
  }

  // 根据类型获取智能体
  getAgentsByType(type: Agent['type']): Agent[] {
    return Array.from(this.agents.values())
      .filter(agent => agent.type === type && agent.status === 'active');
  }

  // 根据能力获取智能体
  getAgentsByCapability(capability: string): Agent[] {
    return Array.from(this.agents.values())
      .filter(agent => 
        agent.capabilities.includes(capability) && 
        agent.status === 'active'
      );
  }

  // 发送消息
  async sendMessage(message: Omit<A2AMessage, 'id' | 'timestamp'>): Promise<string> {
    const fullMessage: A2AMessage = {
      ...message,
      id: uuidv4(),
      timestamp: new Date()
    };

    this.messages.push(fullMessage);
    
    // 限制消息历史长度
    if (this.messages.length > 1000) {
      this.messages = this.messages.slice(-500);
    }

    logger.debug(`Message sent: ${fullMessage.type} from ${fullMessage.from} to ${fullMessage.to}`);
    this.emit('message:sent', fullMessage);

    return fullMessage.id;
  }

  // 分发任务
  async distributeTask(taskData: Omit<TaskDistribution, 'id' | 'status' | 'createdAt'>): Promise<TaskDistribution> {
    const task: TaskDistribution = {
      ...taskData,
      id: taskData.id || uuidv4(),
      status: 'pending',
      createdAt: new Date()
    };

    // 根据任务类型选择合适的智能体
    const suitableAgent = this.selectAgentForTask(task);
    
    if (suitableAgent) {
      task.assignedAgent = suitableAgent.id;
      task.status = 'assigned';
      task.assignedAt = new Date();
      task.estimatedTime = this.estimateTaskTime(task, suitableAgent);

      // 更新智能体状态
      this.updateAgentStatus(suitableAgent.id, 'busy');

      // 发送任务消息给智能体
      await this.sendMessage({
        type: 'task',
        from: 'a2a-hub',
        to: suitableAgent.id,
        payload: {
          taskId: task.id,
          type: task.type,
          parameters: task.parameters,
          documentId: task.documentId,
          priority: task.priority
        },
        priority: task.priority
      });

      logger.info(`Task distributed: ${task.id} to agent ${suitableAgent.id}`);
    } else {
      logger.warn(`No suitable agent found for task: ${task.id}`);
    }

    this.tasks.set(task.id, task);
    this.emit('task:distributed', task);

    return task;
  }

  // 选择合适的智能体执行任务
  private selectAgentForTask(task: TaskDistribution): Agent | null {
    let candidates: Agent[] = [];

    // 根据任务类型选择候选智能体
    switch (task.type) {
      case 'document_analysis':
        candidates = this.getAgentsByType('document_analyzer');
        break;
      case 'document_summary':
      case 'content_processing':
        candidates = this.getAgentsByType('content_processor');
        break;
      case 'ui_update':
      case 'notification':
        candidates = this.getAgentsByType('ui_interface');
        break;
      case 'coordination':
        candidates = this.getAgentsByType('coordinator');
        break;
      default:
        // 尝试根据能力匹配
        candidates = this.getAgentsByCapability(task.type);
    }

    // 过滤可用的智能体
    const availableAgents = candidates.filter(agent => 
      agent.status === 'active' && 
      (!agent.metadata?.maxConcurrentTasks || 
       this.getAgentActiveTasks(agent.id).length < agent.metadata.maxConcurrentTasks)
    );

    if (availableAgents.length === 0) {
      return null;
    }

    // 根据性能和负载选择最佳智能体
    return availableAgents.reduce((best, current) => {
      const bestLoad = this.getAgentActiveTasks(best.id).length;
      const currentLoad = this.getAgentActiveTasks(current.id).length;
      
      if (currentLoad < bestLoad) {
        return current;
      }
      
      if (currentLoad === bestLoad) {
        const bestPerf = best.performance?.successRate || 0;
        const currentPerf = current.performance?.successRate || 0;
        return currentPerf > bestPerf ? current : best;
      }
      
      return best;
    });
  }

  // 获取智能体的活跃任务
  private getAgentActiveTasks(agentId: string): TaskDistribution[] {
    return Array.from(this.tasks.values())
      .filter(task => 
        task.assignedAgent === agentId && 
        (task.status === 'assigned' || task.status === 'running')
      );
  }

  // 估算任务执行时间
  private estimateTaskTime(task: TaskDistribution, agent: Agent): number {
    const baseTime = {
      'document_analysis': 30000,
      'document_summary': 20000,
      'content_processing': 15000,
      'ui_update': 5000,
      'coordination': 10000
    };

    let estimatedTime = baseTime[task.type] || 15000;
    
    // 根据智能体性能调整
    if (agent.performance?.averageResponseTime) {
      estimatedTime = agent.performance.averageResponseTime * 1.2;
    }

    // 根据任务优先级调整
    switch (task.priority) {
      case 'urgent':
        estimatedTime *= 0.8;
        break;
      case 'high':
        estimatedTime *= 0.9;
        break;
      case 'low':
        estimatedTime *= 1.2;
        break;
    }

    return Math.round(estimatedTime);
  }

  // 处理任务完成
  async handleTaskCompletion(taskId: string, result: any, agentId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      logger.warn(`Task not found for completion: ${taskId}`);
      return;
    }

    task.status = 'completed';
    task.completedAt = new Date();
    task.result = result;

    this.tasks.set(taskId, task);

    // 更新智能体状态和性能
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = 'active';
      
      if (agent.performance) {
        agent.performance.tasksCompleted++;
        agent.performance.lastTaskTime = new Date();
        
        // 更新平均响应时间
        if (task.assignedAt) {
          const responseTime = task.completedAt.getTime() - task.assignedAt.getTime();
          agent.performance.averageResponseTime = 
            (agent.performance.averageResponseTime + responseTime) / 2;
        }
      }
      
      this.agents.set(agentId, agent);
    }

    logger.info(`Task completed: ${taskId} by agent ${agentId}`);
    this.emit('task:completed', task);
  }

  // 处理任务错误
  async handleTaskError(taskId: string, error: string, agentId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      logger.warn(`Task not found for error handling: ${taskId}`);
      return;
    }

    task.status = 'error';
    task.error = error;
    task.completedAt = new Date();

    this.tasks.set(taskId, task);

    // 更新智能体状态
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = 'active';
      
      if (agent.performance) {
        const totalTasks = agent.performance.tasksCompleted + 1;
        agent.performance.successRate = 
          agent.performance.tasksCompleted / totalTasks;
      }
      
      this.agents.set(agentId, agent);
    }

    logger.error(`Task failed: ${taskId} by agent ${agentId} - ${error}`);
    this.emit('task:error', task);
  }

  // 获取任务状态
  getTaskStatus(taskId: string): TaskDistribution | undefined {
    return this.tasks.get(taskId);
  }

  // 获取所有任务
  getAllTasks(): TaskDistribution[] {
    return Array.from(this.tasks.values());
  }

  // 获取消息历史
  getMessageHistory(limit: number = 100): A2AMessage[] {
    return this.messages.slice(-limit);
  }

  // 启动心跳监控
  private startHeartbeatMonitoring(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      const timeout = 60000; // 60秒超时

      for (const [id, agent] of this.agents.entries()) {
        if (now.getTime() - agent.lastHeartbeat.getTime() > timeout) {
          if (agent.status !== 'inactive') {
            this.updateAgentStatus(id, 'inactive');
            logger.warn(`Agent marked as inactive due to heartbeat timeout: ${id}`);
          }
        }
      }
    }, 30000); // 每30秒检查一次
  }

  // 启动清理进程
  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldData();
    }, 300000); // 每5分钟清理一次
  }

  // 清理旧数据
  private cleanupOldData(): void {
    const now = new Date();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理旧消息
    this.messages = this.messages.filter(
      message => now.getTime() - message.timestamp.getTime() < maxAge
    );

    // 清理已完成的旧任务
    for (const [id, task] of this.tasks.entries()) {
      if (
        (task.status === 'completed' || task.status === 'error') &&
        task.completedAt &&
        now.getTime() - task.completedAt.getTime() > maxAge
      ) {
        this.tasks.delete(id);
      }
    }

    logger.debug('Old data cleanup completed');
  }

  // 关闭服务
  shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    logger.info('A2A Hub shutdown completed');
  }
}

export const a2aHub = new A2AHub();
export { Agent, A2AMessage, TaskDistribution };