import express from 'express';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { a2aHub } from '../services/a2aHub';
import { agentService } from '../services/agentService';
import { logger } from '../utils/logger';

const router = express.Router();

// 获取所有智能体状态
router.get('/status', asyncHandler(async (req, res) => {
  const agents = await a2aHub.getRegisteredAgents();
  const agentStats = await agentService.getAgentStatistics();
  
  res.json({
    success: true,
    data: {
      agents: agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        type: agent.type,
        status: agent.status,
        capabilities: agent.capabilities,
        lastHeartbeat: agent.lastHeartbeat,
        tasksCompleted: agentStats[agent.id]?.tasksCompleted || 0,
        averageResponseTime: agentStats[agent.id]?.averageResponseTime || 0
      })),
      totalCount: agents.length,
      activeCount: agents.filter(agent => agent.status === 'active').length,
      summary: {
        totalTasks: Object.values(agentStats).reduce((sum, stat) => sum + (stat.tasksCompleted || 0), 0),
        averageResponseTime: Object.values(agentStats).reduce((sum, stat) => sum + (stat.averageResponseTime || 0), 0) / agents.length || 0
      }
    }
  });
}));

// 获取特定智能体详情
router.get('/:agentId', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  
  const agent = await a2aHub.getAgent(agentId);
  if (!agent) {
    throw new CustomError('Agent not found', 404);
  }

  const agentDetails = await agentService.getAgentDetails(agentId);
  const recentTasks = await agentService.getAgentRecentTasks(agentId, 10);
  
  res.json({
    success: true,
    data: {
      ...agent,
      details: agentDetails,
      recentTasks: recentTasks.map(task => ({
        id: task.id,
        type: task.type,
        status: task.status,
        startTime: task.startTime,
        endTime: task.endTime,
        duration: task.duration,
        documentId: task.documentId
      }))
    }
  });
}));

// 创建任务分发
router.post('/tasks', asyncHandler(async (req, res) => {
  const { taskType, documentId, parameters = {} } = req.body;
  
  if (!taskType || !documentId) {
    throw new CustomError('taskType and documentId are required', 400);
  }

  // 验证任务类型
  const validTaskTypes = ['analyze', 'translate', 'summarize', 'process'];
  if (!validTaskTypes.includes(taskType)) {
    throw new CustomError(`Invalid task type. Must be one of: ${validTaskTypes.join(', ')}`, 400);
  }

  try {
    // 通过A2A Hub分发任务
    const task = await a2aHub.distributeTask({
      type: taskType,
      documentId,
      parameters,
      priority: parameters.priority || 'normal',
      timeout: parameters.timeout || 300000 // 5分钟默认超时
    });

    res.json({
      success: true,
      data: {
        taskId: task.id,
        assignedAgent: task.assignedAgent,
        estimatedTime: task.estimatedTime,
        status: task.status
      },
      message: 'Task created and assigned successfully'
    });
  } catch (error) {
    logger.error('Task distribution failed:', error);
    throw new CustomError('Failed to distribute task', 500);
  }
}));

// 获取任务状态
router.get('/tasks/:taskId', asyncHandler(async (req, res) => {
  const { taskId } = req.params;
  
  const task = await agentService.getTaskById(taskId);
  if (!task) {
    throw new CustomError('Task not found', 404);
  }

  res.json({
    success: true,
    data: {
      id: task.id,
      type: task.type,
      status: task.status,
      progress: task.progress,
      agentId: task.agentId,
      documentId: task.documentId,
      result: task.result,
      error: task.error,
      startTime: task.startTime,
      endTime: task.endTime,
      duration: task.duration
    }
  });
}));

// 取消任务
router.delete('/tasks/:taskId', asyncHandler(async (req, res) => {
  const { taskId } = req.params;
  
  const task = await agentService.getTaskById(taskId);
  if (!task) {
    throw new CustomError('Task not found', 404);
  }

  if (task.status === 'completed' || task.status === 'failed') {
    throw new CustomError('Cannot cancel completed or failed task', 400);
  }

  await a2aHub.cancelTask(taskId);
  
  res.json({
    success: true,
    message: 'Task cancelled successfully'
  });
}));

// 智能体健康检查
router.post('/:agentId/health', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  
  const agent = await a2aHub.getAgent(agentId);
  if (!agent) {
    throw new CustomError('Agent not found', 404);
  }

  const healthStatus = await a2aHub.checkAgentHealth(agentId);
  
  res.json({
    success: true,
    data: {
      agentId,
      status: healthStatus.status,
      responseTime: healthStatus.responseTime,
      lastCheck: healthStatus.timestamp,
      details: healthStatus.details
    }
  });
}));

// 获取智能体性能指标
router.get('/:agentId/metrics', asyncHandler(async (req, res) => {
  const { agentId } = req.params;
  const { timeRange = '1h' } = req.query;
  
  const agent = await a2aHub.getAgent(agentId);
  if (!agent) {
    throw new CustomError('Agent not found', 404);
  }

  const metrics = await agentService.getAgentMetrics(agentId, timeRange as string);
  
  res.json({
    success: true,
    data: {
      agentId,
      timeRange,
      metrics: {
        tasksProcessed: metrics.tasksProcessed,
        averageResponseTime: metrics.averageResponseTime,
        successRate: metrics.successRate,
        errorRate: metrics.errorRate,
        throughput: metrics.throughput,
        cpuUsage: metrics.cpuUsage,
        memoryUsage: metrics.memoryUsage
      }
    }
  });
}));

export { router as agentRoutes };