import React, { useState, useRef, useCallback } from 'react';
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Download,
  Eye,
  Trash2
} from 'lucide-react';
import { useAppStore } from '../store/appStore';
import { useSocket } from '../hooks/useSocket';
import { cn } from '../utils/cn';
import { toast } from 'sonner';

interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export default function DocumentUpload() {
  const { documents, addDocument, updateDocument } = useAppStore();
  const { socket } = useSocket();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadFile[] = Array.from(files).map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      progress: 0,
      status: 'pending'
    }));

    setUploadFiles(prev => [...prev, ...newFiles]);
  }, []);

  // 处理拖拽
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  // 上传文件
  const uploadFile = useCallback(async (uploadFile: UploadFile) => {
    const { id, file } = uploadFile;
    
    setUploadFiles(prev => prev.map(f => 
      f.id === id ? { ...f, status: 'uploading' } : f
    ));

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('uploadId', id);

      // 监听上传进度
      if (socket) {
        socket.on('file_upload_progress', (data: { uploadId: string; progress: number }) => {
          if (data.uploadId === id) {
            setUploadFiles(prev => prev.map(f => 
              f.id === id ? { ...f, progress: data.progress } : f
            ));
          }
        });
      }

      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`上传失败: ${response.statusText}`);
      }

      const result = await response.json();
      
      setUploadFiles(prev => prev.map(f => 
        f.id === id ? { ...f, status: 'completed', progress: 100 } : f
      ));

      // 添加到文档列表
      addDocument({
        id: result.id,
        name: file.name,
        filename: result.filename || file.name,
        originalName: file.name,
        type: file.type,
        mimeType: file.type,
        size: file.size,
        uploadTime: new Date().toISOString(),
        uploadedAt: new Date(),
        status: 'uploaded',
        progress: 100,
        content: '',
        description: ''
      });

      toast.success(`文件 "${file.name}" 上传成功`);

    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      
      setUploadFiles(prev => prev.map(f => 
        f.id === id ? { ...f, status: 'error', error: errorMessage } : f
      ));

      toast.error(`文件 "${file.name}" 上传失败: ${errorMessage}`);
    }
  }, [socket, addDocument]);

  // 开始上传所有文件
  const startUpload = useCallback(() => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    pendingFiles.forEach(uploadFile);
  }, [uploadFiles, uploadFile]);

  // 移除文件
  const removeFile = useCallback((id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id));
  }, []);

  // 清空已完成的文件
  const clearCompleted = useCallback(() => {
    setUploadFiles(prev => prev.filter(f => f.status !== 'completed'));
  }, []);

  // 重试上传
  const retryUpload = useCallback((uploadFile: UploadFile) => {
    setUploadFiles(prev => prev.map(f => 
      f.id === uploadFile.id ? { ...f, status: 'pending', error: undefined } : f
    ));
    setUploadFiles(prev => prev.map(f => 
      f.id === uploadFile.id 
        ? { ...f, status: 'pending', error: undefined }
        : f
    ));
  }, [uploadFile]);

  // 删除文档
  const deleteDocument = useCallback(async (docId: string) => {
    try {
      const response = await fetch(`/api/documents/${docId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('删除失败');
      }

      updateDocument(docId, { status: 'error' });
      toast.success('文档删除成功');
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('文档删除失败');
    }
  }, [updateDocument]);

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄';
    if (type.includes('word') || type.includes('doc')) return '📝';
    if (type.includes('excel') || type.includes('sheet')) return '📊';
    if (type.includes('powerpoint') || type.includes('presentation')) return '📈';
    if (type.includes('text')) return '📃';
    if (type.includes('image')) return '🖼️';
    return '📄';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const pendingCount = uploadFiles.filter(f => f.status === 'pending').length;
  const uploadingCount = uploadFiles.filter(f => f.status === 'uploading').length;
  const completedCount = uploadFiles.filter(f => f.status === 'completed').length;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">文档上传</h1>
        <p className="text-muted-foreground mt-1">
          上传文档以进行智能分析和处理
        </p>
      </div>

      {/* 上传区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 文件上传 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 拖拽上传区域 */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isDragOver 
                ? "border-primary bg-primary/5" 
                : "border-muted-foreground/25 hover:border-primary/50"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              拖拽文件到此处或点击上传
            </h3>
            <p className="text-muted-foreground mb-4">
              支持 PDF、Word、Excel、PowerPoint、文本文件等格式
            </p>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="btn-primary"
            >
              选择文件
            </button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md"
              onChange={(e) => handleFileSelect(e.target.files)}
            />
          </div>

          {/* 上传队列 */}
          {uploadFiles.length > 0 && (
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-foreground">上传队列</h3>
                <div className="flex items-center space-x-2">
                  {pendingCount > 0 && (
                    <button
                      onClick={startUpload}
                      className="btn-primary btn-sm"
                    >
                      开始上传 ({pendingCount})
                    </button>
                  )}
                  {completedCount > 0 && (
                    <button
                      onClick={clearCompleted}
                      className="btn-secondary btn-sm"
                    >
                      清空已完成
                    </button>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                {uploadFiles.map((uploadFile) => (
                  <div key={uploadFile.id} className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                    <div className="text-2xl">
                      {getFileIcon(uploadFile.file.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-foreground truncate">
                          {uploadFile.file.name}
                        </p>
                        {getStatusIcon(uploadFile.status)}
                      </div>
                      
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(uploadFile.file.size)}
                      </p>
                      
                      {uploadFile.status === 'uploading' && (
                        <div className="mt-2">
                          <div className="progress-bar">
                            <div 
                              className="progress-fill" 
                              style={{ width: `${uploadFile.progress}%` }}
                            />
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {uploadFile.progress}%
                          </span>
                        </div>
                      )}
                      
                      {uploadFile.error && (
                        <p className="text-xs text-red-500 mt-1">
                          {uploadFile.error}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-1">
                      {uploadFile.status === 'error' && (
                        <button
                          onClick={() => retryUpload(uploadFile)}
                          className="p-1 text-blue-500 hover:bg-blue-100 rounded"
                          title="重试"
                        >
                          <Loader2 className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => removeFile(uploadFile.id)}
                        className="p-1 text-red-500 hover:bg-red-100 rounded"
                        title="移除"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 上传统计 */}
        <div className="space-y-6">
          {/* 统计卡片 */}
          <div className="bg-card rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">上传统计</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">等待上传</span>
                <span className="text-sm font-medium text-foreground">{pendingCount}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">正在上传</span>
                <span className="text-sm font-medium text-blue-600">{uploadingCount}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">上传完成</span>
                <span className="text-sm font-medium text-green-600">{completedCount}</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">总文档数</span>
                  <span className="text-sm font-bold text-foreground">{documents.length}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 最近上传 */}
          <div className="bg-card rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">最近上传</h3>
            
            <div className="space-y-3">
              {documents.slice(0, 5).map((doc) => (
                <div key={doc.id} className="flex items-center space-x-3">
                  <div className="text-lg">
                    {getFileIcon(doc.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {doc.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(doc.size)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <button
                      className="p-1 text-blue-500 hover:bg-blue-100 rounded"
                      title="查看"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      className="p-1 text-green-500 hover:bg-green-100 rounded"
                      title="下载"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => deleteDocument(doc.id)}
                      className="p-1 text-red-500 hover:bg-red-100 rounded"
                      title="删除"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}